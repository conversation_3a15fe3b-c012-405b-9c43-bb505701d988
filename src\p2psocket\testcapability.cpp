// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#include <string>
#include <vector>
#include <iostream>
#include <chrono>
#include <thread>
#include <cstring>
#include "p2psocket.h"
#include "cert_wrapper.h"
enum TEST_LOG_LEVEL {
    LOG_ERROR = 0,
    LOG_WARN = 1,
    LOG_INFO = 2,
    LOG_DEBUG = 3,
    LOG_VERBOSE = 4
};

class LLog {
public:
    static void Log(const char* message) {
        std::cout << message << std::endl;
    }

    static void Log(int level, const std::string& message) {
        std::cout << "[" << level << "] " << message << std::endl;
    }

    static void Log(const std::string& message) {
        std::cout << message << std::endl;
    }

    template<typename... Args>
    static void Log(int level, const char* format, Args... args) {
        char buffer[1024];
        snprintf(buffer, sizeof(buffer), format, args...);
        std::cout << "[" << level << "] " << buffer << std::endl;
    }
};

static std::string server_key =
    "-----BEGIN PRIVATE "
    "KEY-----"
    "\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCalSDHlHgpg94z\nxxVQIr"
    "Dvfzt+DngpIG4ikaW1pKWBx/+6RIZglw2vwvATEOu6kbjExd2GLmGwGBu/\n+hR7owvU/"
    "pMnoCnidX36y3CckDvhAuTLyNiQFp11SGI5wi69RpK6bA5yLLmPBQO8\n4lH9FVtyOmtjzWKnS"
    "jx+Ta2bHkuFS69VxzeF660LKIRx9V3lF67Llt+"
    "iueNnvpE7\nKNAW2VZIGsKvzsrI92ITrhPZ1qII3DKX+ApyjFXFANP+"
    "E7OAOOhGckh0J8ZjAzzI\ny1ZVyJQ2MheS4wRxrelVXrr+1BTYo+VX9vsvfGJ/pvtKGnLf/"
    "BuAMZZMwzTni8/P\n1NJJDRfHAgMBAAECggEABLx8/"
    "KB37g20STHYXVQyx6PGVt5qoBf2R+jegupAh/"
    "Fw\nzGw9EciPCsuWP39NObItTxog53OUWqjicMdgyUj9m9ERAS3PGrku2vhQwvtIWmSU\nTgoh"
    "WIjY2jcVi19/"
    "vxzicgPNjYIBTXiB3M+yY2a8HovbrnskgqurntMSdqY++"
    "h6D\nUzCF5YVTFHhPpjqRl0dcGXQexKr8+Xz4TZKwDgWdBZ+su/U4xCL0FJC/"
    "nMNZZzio\nPd8pBYboQ7hA5Be0jv/ZRs/"
    "qfPG6zTbghrO24wjEHL2e0eXuZOMNW9n52TUptiIB\ncXEygchkfAwo5aBqX/"
    "Ky+YnFRG9d8+tnLN7RJmTrgQKBgQC8H9q/"
    "nR4Rx5M4RwZJ\nPkhm5pvxBNIInTpAcyWevTj+BTe04grerNSQXCYzX2g6HHWGX+"
    "QLvvw6zxauhDsD\nIZNog7BA45DYuPxPsRb1wZt7+"
    "GksQs7RmbcRqmyuuObmpONvCK4Ee5HngyPD4Eqq\nXuLZgJA6aY47a6FjaSE1e+"
    "K5nwKBgQDSWyuQowm0iIFk1PpSoZbBKUlwC9VJZEbk\nWHW57c/kmPs7PK/"
    "xCU2g0nP3O9ar7i2gKuU4PULfeGX9JR3147Gz8orJv/zz6xaD\nVFvzm1wercFTZ/"
    "OGHieDJ3XpMKJaqJGZkT5C0R+8txNqLn0rY+Tehqy6+"
    "AjQRKrL\nOAqO4MpA2QKBgGtghzw9ku81CeviZk0iFrNdR38PcE7oZ1poHv33JXOgSMafCvNE"
    "\nlON9JXTHLTeWDOLREto9fbyXfWvJH+HOpVPPbqfq/D8nfySBgQhvJK9i/6z6yQ/L\n/"
    "DlCHFyyP2FAlmxG+QSn/"
    "4S0TpK8EAIkvLjG8AvXOg7ihTC3zfO4LZYjAoGAIeuh\nsskXn40DkIbndrun264UsYS2+"
    "Aa7h8bb9QdsJqikmoDGvk+JvQ0ytgNoMoFNyi4g\nmFkIl5CKoa+"
    "CjUwSM7pAtxfGam7WSocn7Lh4ulm6ewCgPFhQds2+LcQx6fyUvfa1\n24BtZbj/"
    "4Hdup7iMB3YgFGY6xZrND8gRq8iD1MECgYAgO5LX1ErfKB9+OeMm5Vct\nAQQB1hFShq/"
    "wuafCTVFV4ZeJT+KFYofDjp7V9Khqp4F61XqEk0lEbjQDYAsanYSv\n0RUowVZ2xU9FW7SKD++"
    "AiQKEZXeFrmhZKmTvYItzifqSjS18bmpWZcPaDCznZ7fq\ncOF7SLbtlbLWjI+FQzyXDw==\n-"
    "----END PRIVATE KEY-----";
static std::string server_cert =
    "-----BEGIN "
    "CERTIFICATE-----"
    "\nMIIC7DCCAdQCAQEwDQYJKoZIhvcNAQELBQAwPDELMAkGA1UEBhMCQ04xDzANBgNV\nBAoMBk"
    "xlbm92bzEPMA0GA1UEAwwGTGVub3ZvMQswCQYDVQQLDAJEQzAeFw0yNDAy\nMDUwMzI1MjdaFw"
    "0yNTAyMDQwMzI1MjdaMDwxCzAJBgNVBAYTAkNOMQ8wDQYDVQQK\nDAZMZW5vdm8xDzANBgNVBA"
    "MMBkxlbm92bzELMAkGA1UECwwCREMwggEiMA0GCSqG\nSIb3DQEBAQUAA4IBDwAwggEKAoIBAQ"
    "CalSDHlHgpg94zxxVQIrDvfzt+DngpIG4i\nkaW1pKWBx/"
    "+6RIZglw2vwvATEOu6kbjExd2GLmGwGBu/+hR7owvU/"
    "pMnoCnidX36\ny3CckDvhAuTLyNiQFp11SGI5wi69RpK6bA5yLLmPBQO84lH9FVtyOmtjzWKnS"
    "jx+\nTa2bHkuFS69VxzeF660LKIRx9V3lF67Llt+"
    "iueNnvpE7KNAW2VZIGsKvzsrI92IT\nrhPZ1qII3DKX+ApyjFXFANP+"
    "E7OAOOhGckh0J8ZjAzzIy1ZVyJQ2MheS4wRxrelV\nXrr+1BTYo+VX9vsvfGJ/pvtKGnLf/"
    "BuAMZZMwzTni8/"
    "P1NJJDRfHAgMBAAEwDQYJ\nKoZIhvcNAQELBQADggEBAAFY5ybddSZ5i3n4tZieM7c8NlY9Sdw"
    "o01p3Dvsx84Ri\nK3WplaWtfYgSOB5eEgcdNosspTlU1fLuqDSN87IweucTfTVvw8gnbd5DNJG"
    "2QmkD\n8+"
    "kQs1oJEarUOMJQDuG8jxtfFNMZBiEwKzQZclObDMIGHwaujVRM7FYE2H9xP2w3\nZvIbywGMvt"
    "7lBtinA6HcGyaGzqMLFTUf4zXM1qQc3wCd/"
    "eCIbG1KkR0Ht3qPQCsX\nGKZnSfOPonz9rwmlypS1g0uABAH2VNZiJNmiKY1PEmxT2ka2rrYcA"
    "+/ZcxEG5hFr\nI4oGui1jJns/DyDY4e9jyoORhi0H8mhx9jWODdcJcl4=\n-----END "
    "CERTIFICATE-----";
static std::string server_fp =
    "21C6E2137931A0464484A6B40E5EF94ECF54449E0134F1857F8B98627B1C5C2A";
static std::string cli_key =
    "-----BEGIN PRIVATE "
    "KEY-----"
    "\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDmPuSYaARmcHcl        "
    "\nnaz7HIscFq2PPU9183/0hrb6a4qk0EHJi2YFY8KFUnxyjI2LWVnCE/CuTODXr3aq        "
    "\nHYJlNk9ZqLomDGk2f0JIQQh05JvhptZCOpgRBH/ykoJef8XPk4A53SYE0ICQcoL6        "
    "\nNPXwCszIrp5Et5LIMmokydJRpEuMeQhKeIrMsDhQLD81sIDuouy6i2rzUMUbc0Yz        "
    "\nWfJkwBubnGoX0ktgpB6kWY9SR70kVGGxDuBF91aGNLc4J4GmBMQEy9JVkM036xVb        "
    "\n/RfGMqTx4bzshyDz4QChb362EkNuDvP+n/Nd1iGc/D8Z6e7KXYJZBQ+BC/T9gm31        "
    "\nR3DtQwYlAgMBAAECggEAAUplGlniV0mD9OM8cljArydoiBOD+ZTIn8JW+82m8jqP        "
    "\nMGqGZ6jmkOCbavk+k0x12NdwM2+h4igxs9+yVDR7FhmlF8MT552ntqxmE6o8Gb01        "
    "\nBvdL1H0pU0yMgvU8iYxnU/TZUVm72QpKI83nerpV6at+DFXYhNswoY27M25h+Svz        "
    "\nNkjMhPGP1/0TTIabi1V9cNG92XWIbo21MYN9yU5vLYE6A+Wa1grZjKxd9KbLucpb        "
    "\nepG+ct6XJNitQp7kbLwgtuP6mW9O/ne/7Mv3afq5Uf/ZTZMV8Kr855vJQOfic1yq        "
    "\nbIEY1t9l2DNxnmyQ+fqoCre7Ee+sJ+D5TbxhDFGTtwKBgQD+zXCYYahboiG8kIsU        "
    "\nJyHD1ZCKUnRFmTdS5ltvPUduXvd3UBpVriyfWDVAAXOdHXq1mVvRozvZn7jSnLu9        "
    "\n9OkdqUZp9q15A/"
    "oN93LdFnNVcJ9XkL7rEyOJcL4eZIxrIHJPvsQaThh5C+Q9JUWq\nof1esUoPk2Od/"
    "wDSBPmWgjOOZwKBgQDnU+"
    "h5ykvBhDxJFTVN9WycHEELS9NdjPz1\n4lgM08r32CmjZniuUJOOkAuUpC2b6O0yMQedXXdVq7"
    "kxZ2Xb8aGiKdvgV5XTr9CA\nBLJNPbaKSNgpVPDRWhhn/"
    "bA6WsjYLl7rNjAHeWZyvUsar9743ThacnqWb3RooQkS\nqJkqhRsXkwKBgQC6wZNMbQ+"
    "8EgrOLddybvgllIv7hlHojVNqdKMB1pVbhUm2bsTO\nlfno1Ps6rZWyy9+"
    "b6QrOjekZKiOKYwyMP0z5y49rrT0anNqTkBHlkEX6krCHuvTk\nUcPTQgpnNmVhQ4flgTo02Zb"
    "AagHg+"
    "0ejuFWAKUvSPMJgFH7icHkLYuUs8wKBgHIH\nIXRPbLJSbakUxDbOgKAfKUzZLrJRmivWS9Le3"
    "5D2Q+ggzeIXI/VFOgAmQbk1wx5X\nnspKVVWslVguAL6/"
    "SA54lcLrSZaUdrF0HzQF9KILsEoHbcG0qI0yNxk8wP4XNYC6\nMEiu5fovJwNbqAyCd5HWFmlr"
    "PTWKE7/"
    "XzdBw1l7zAoGAYZvmEuq8unSNGtBlyqoD\na4lbyFKcu5FsdRgjn1k53xejSbuobWaUHQ3jER+"
    "mXKWqiFaXBLPBzy+"
    "0UBbomWWR\nI8oGLLg37fdOruh5leMMHEidT92016l0lhvOrONvbrU741kiaykkeqlscuGqP//"
    "z\nUY1Zl1OAwk0N+C6sOYzx/vg=\n-----END PRIVATE KEY-----";
static std::string cli_cert =
    "-----BEGIN "
    "CERTIFICATE-----"
    "\nMIIC7DCCAdQCAQEwDQYJKoZIhvcNAQELBQAwPDELMAkGA1UEBhMCQ04xDzANBgNV\nBAoMBk"
    "xlbm92bzEPMA0GA1UEAwwGTGVub3ZvMQswCQYDVQQLDAJEQzAeFw0yNDAy\nMDUwMzI5MDJaFw"
    "0yNTAyMDQwMzI5MDJaMDwxCzAJBgNVBAYTAkNOMQ8wDQYDVQQK\nDAZMZW5vdm8xDzANBgNVBA"
    "MMBkxlbm92bzELMAkGA1UECwwCREMwggEiMA0GCSqG\nSIb3DQEBAQUAA4IBDwAwggEKAoIBAQ"
    "DmPuSYaARmcHclnaz7HIscFq2PPU9183/0\nhrb6a4qk0EHJi2YFY8KFUnxyjI2LWVnCE/"
    "CuTODXr3aqHYJlNk9ZqLomDGk2f0JI\nQQh05JvhptZCOpgRBH/"
    "ykoJef8XPk4A53SYE0ICQcoL6NPXwCszIrp5Et5LIMmok\nydJRpEuMeQhKeIrMsDhQLD81sID"
    "uouy6i2rzUMUbc0YzWfJkwBubnGoX0ktgpB6k\nWY9SR70kVGGxDuBF91aGNLc4J4GmBMQEy9J"
    "VkM036xVb/RfGMqTx4bzshyDz4QCh\nb362EkNuDvP+n/Nd1iGc/D8Z6e7KXYJZBQ+BC/"
    "T9gm31R3DtQwYlAgMBAAEwDQYJ\nKoZIhvcNAQELBQADggEBAGkrkZpBCXLNUwFfMIbVNYTLPt"
    "yxLnFVb6L2HaFO8VKW\nVpDtcW4iqNSQswtskpKKlAZAGHWv4i88NK7oB9HNcOeokI1vyW+"
    "pKL8XlRDi3Uc1\nPTjuYOYPBjqzvD5hEDAWfPOrSunf8vJ3wjMRSjeqZ0BvaaAburGwaCAlfNI"
    "wbMR3\ndnlUAhdv9gtVdfNVIC6t3+T7WW9Uiu+rD9w0GfGmuYb23TtYLg0kuzD7HMi3a/"
    "OA\na5svHYrU1rUGy+"
    "oF3eKcIS0sJj11P3EHmEj3Gt7VUtpuVwLv3ex6cz05y0JnvL6x\n0dW98D0pi77HmLvuAQVbOo"
    "3AukaF7D66p76FoXCHCWw=\n-----END CERTIFICATE-----";
static std::string cli_fp =
    "13FBAB5055F4398618B1E730039243B55F866BA764C4F4F62506679629397BEC";


uint64_t GetCurrentTimeFS() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}
int PKT_SIZE = 1200 * 5;
static bool verifycallback(P2P_SOCKET soc, const char* x509){
  LLog::Log(LOG_INFO,"gtest verifycallback soc = %d, x509 = %s\n",soc,x509);
  return true;
}
int main(int argc, char* argv[]) {
  std::string ip = "0";
  std::string isssl = "0";
  std::string num = "8";
  int type = 3;
  int sndmode = 1;//default direct mode
  int recvmode = 0;// default buffer recv mode
  int BUFFER_SIZE =64 * 1024;
  for (int i = 1; i < argc; i++) {
    std::string arg = argv[i];
    if (arg == "-ip") {
      ip = argv[++i];
    } else if (arg == "-ssl") {
      isssl = argv[++i];
    } else if (arg == "-num") {
      num = argv[++i];
    } else if (arg == "-type") {
      type = std::stoi(argv[++i]);
    } else if(arg == "-sendmode"){
      sndmode =  std::stoi(argv[++i]);
    } else if(arg == "-recvmode"){
      recvmode =  std::stoi(argv[++i]);
    }else if (arg == "-h") {
      std::cout << "Usage: program -ip 127.0.0.1 [-ssl 0] [-num 8]"
                << std::endl;
      return 0;
    }
  }
  if (ip == "0") {
    long size = 0;
    SocketOptions option;
    CertHandle handle = Cert_Create();
    option.mode = MODE_SERVER;
    option.cert_verify = verifycallback;
    option.cert = handle.Cert_GetX509Str();//server_cert.c_str();
    option.privatekey = handle.Cert_GetPkeyStr(); //server_key.c_str();
    option.wokernum = std::stoi(num);
    option.type = (enum SOCKET_TYPE)type;
    option.log_level = P2P_LOG_INFO;
    option.log_path = "e:\\server_log.txt";
    std::cout << "type is " << option.type << std::endl;
    LLog::Log(LOG_INFO,"fp = %s",handle.Cert_GetLocalX509CertFingerprint());
    P2P_SOCKET soc = P2pCreate(&option);
    P2pSetConnTimeout(soc, 10 * 1000);
    P2pSetSendMode(soc, sndmode);
    P2pSetReadMode(soc, recvmode);
    int r = P2pBind(soc, "0.0.0.0", 4433);
    printf("---P2pBind----r=%d",r);
    r= P2pListen(soc);
    printf("---P2pListen----r=%d", r);
    LLog::Log("---listening----");
    char ip[128] = {0};
    int port = 0;
    P2P_SOCKET cli = P2pAccept(soc, ip, sizeof(ip), &port);
    printf("---P2pAccept----cli=%p", cli);
    char* buffer = (char*)malloc(BUFFER_SIZE);
    if (buffer == nullptr)
      return 0;
    std::string recv_data;
    LLog::Log("start to receive data");
    uint64_t start = GetCurrentTimeFS();
    uint64_t start_ = start;
    uint64_t end_ = start_;
    std::vector<float> bws;
    int size_mb = 0;
    do {
      memset(buffer, 0, BUFFER_SIZE);
      int bytes = P2pRead(cli, buffer, BUFFER_SIZE);
      //printf("----p2pread----bytes = %d\n", bytes);
      if (bytes <= 0)
        break;
      size += bytes;

      if (size >> 20 >= 120) {
        uint64_t delta = GetCurrentTimeFS() - start;
        float bw = (size >> 20) / (float)delta * 1000 * 8;
        bws.push_back(bw);
        size_mb += size >> 20;
        std::cout << "===========bandwidth is " + std::to_string(bw) +
                         " delta is " + std::to_string(delta) + " size is " +
                         std::to_string(size >> 20) + "\n";
        size = 0;
        start = GetCurrentTimeFS();
      }
      if (size_mb >= PKT_SIZE * 2)
        end_ = GetCurrentTimeFS();
    } while (true);

    if (size != 0) {
      size_mb += size >> 20;
    }
    // std::cout << "band width total is "
    //           << PKT_SIZE * 2 * 8 * 1000 / (float)(end_ - start_)
    //           << " mbps size_mb = " << size_mb << "\n";
    float sum = 0;
    for (float f : bws) {
      sum += f;
    }
    LLog::Log(LOG_ERROR,
              "band width avg is " + std::to_string(sum / bws.size()));
    P2pClose(cli);
    P2pClose(soc);
    LLog::Log("ReceiveData end\n");
  } else {
    long size = 0;

    SocketOptions option;
    CertHandle handle = Cert_Create();
    option.mode = MODE_CLIENT;
    option.cert_verify = verifycallback;
    option.cert = handle.Cert_GetX509Str();//cli_cert.c_str();
    option.privatekey = handle.Cert_GetPkeyStr(); //cli_key.c_str();
    option.wokernum = std::stoi(num);
    option.type = (enum SOCKET_TYPE)type;
    option.log_level = P2P_LOG_INFO;
    option.log_path = "e:\\client_log.txt";
    std::cout << "type is " << option.type << std::endl;
    LLog::Log(LOG_INFO,"fp = %s",handle.Cert_GetLocalX509CertFingerprint());
    P2P_SOCKET soc = P2pCreate(&option);
    P2pSetSendMode(soc, sndmode);
    P2pSetReadMode(soc, recvmode);
    std::string address;
    if (ip.empty()) {
      address = "**************";
    } else {
      address = ip;
    }
    int ret = P2pConnect(soc, address.c_str(), 4433);
    if (ret < 0) {
      LLog::Log("P2pConnect error");
      ret = P2pConnect(soc, address.c_str(), 4433);
      if (ret < 0) {
        LLog::Log("connect error");
        return 0;
      }
      // return 0;
    }
    char buffer[0xffff];
    std::string recv_data;
    LLog::Log("start to send data");
    int bytesRead;
    std::string outtext;
    outtext.resize(BUFFER_SIZE, '1');
    for (int i = 0; i < BUFFER_SIZE; i++) {
      outtext[i] = i % 128;
    }
    uint64_t start = GetCurrentTimeFS();
    uint64_t start_ = start;
    uint64_t end_ = start_;
    int size_mb = 0;
    std::vector<float> bws;
    for (int i = 0; i < PKT_SIZE; i++) {  // 220
      int bytesSent = 0;
      int total = 0;
      while (true) {
        if ((bytesSent = P2pWrite(soc, outtext.data(),
                                  outtext.length() - total)) == -1) {
            LLog::Log("Senderror break\n");
          break;
        } else {
          total += bytesSent;
          size += bytesSent;
          if (size >> 20 >= 120) {
            uint64_t delta = GetCurrentTimeFS() - start;
            float bw = (size >> 20) / (float)delta * 1000 * 8;
            bws.push_back(bw);
            LLog::Log(LOG_ERROR, "===========bandwidth is " +
                                     std::to_string(bw) + " delta is " +
                                     std::to_string(delta) + " size is " +
                                     std::to_string(size >> 20));
            std::cout << "===========bandwidth is " << std::to_string(bw)
                      << " delta is " << std::to_string(delta) << " size is "
                      << std::to_string(size >> 20) << std::endl;
            size = 0;
            start = GetCurrentTimeFS();
          }
          if (total == outtext.length()) {
            size_mb += total >> 20;
            break;
          }
        }
      }
      if (bytesSent < 0)
          break;
    }
    // LLog::Log(LOG_ERROR,
    //           "band width total is " +
    //               std::to_string(PKT_SIZE * 2 * 8 * 1000 /
    //                              (float)(GetCurrentTimeFS() - start_)) +
    //               "mbps size_mb is " + std::to_string(size_mb));
    // std::cout << "band width total is " +
    //                  std::to_string(PKT_SIZE * 2 * 8 * 1000 /
    //                                 (float)(GetCurrentTimeFS() - start_)) +
    //                  "mbps size_mb is " + std::to_string(size_mb) + "\n";
    float sum = 0;
    for (float f : bws) {
      sum += f;
    }
    LLog::Log(LOG_ERROR,
              "band width avg is " + std::to_string(sum / bws.size()));
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    P2pClose(soc);

    LLog::Log("SendData end\n");
  }
  return 0;
}
