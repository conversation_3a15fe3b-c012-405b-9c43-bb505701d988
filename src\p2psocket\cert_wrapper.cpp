// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#include "cert_wrapper.h"

#include "cert.h"
#include "lenlog.h"
using namespace datatunnel;
#ifndef _WIN32
extern "C" {

CertHandle Cert_Create() {
  LLog::Log(LOG_INFO, "Cert_Create");
  Cert* cert = new Cert();
  if (!cert) {
    return nullptr;
  }
  return reinterpret_cast<CertHandle>(cert);
}

void Cert_Destroy(CertHandle handle) {
  LLog::Log(LOG_INFO, "Cert_Destroy");
  if (!handle) {
    LLog::Log(LOG_ERROR, "Cert_Destroy handle is null");
    return;
  }
  delete reinterpret_cast<Cert*>(handle);
}

const char* Cert_GetLocalX509CertFingerprint(CertHandle handle) {
  LLog::Log(LOG_INFO, "Cert_GetLocalX509CertFingerprint");
  if (!handle) {
    LLog::Log(LOG_ERROR, "Cert_GetLocalX509CertFingerprint handle is null");
    return nullptr;
  }
  Cert* cert = reinterpret_cast<Cert*>(handle);
  return cert->GetLocalX509CertFingerprint().c_str();
}

const char* Cert_GetPkeyStr(CertHandle handle) {
  LLog::Log(LOG_INFO, "Cert_GetPkeyStr");
  if (!handle) {
    LLog::Log(LOG_ERROR, "Cert_GetPkeyStr handle is null");
    return nullptr;
  }
  Cert* cert = reinterpret_cast<Cert*>(handle);
  return cert->GetPkeyStr().c_str();
}

const char* Cert_GetX509Str(CertHandle handle) {
  LLog::Log(LOG_INFO, "Cert_GetX509Str");
  if (!handle) {
    LLog::Log(LOG_ERROR, "Cert_GetX509Str handle is null");
    return nullptr;
  }
  Cert* cert = reinterpret_cast<Cert*>(handle);
  return cert->GetX509Str().c_str();
}
}
#endif