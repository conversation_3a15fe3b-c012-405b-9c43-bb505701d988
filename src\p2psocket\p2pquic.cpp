// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.

#include "p2pquic.h"
#include "common_defines.h"
#include "lenlog.h"

#include <msquic.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "quic_platform.h"
#include "quic_cert.h"
#ifdef _WIN32
#include <process.h>
#include <windows.h>
#include <winsock2.h>
#include <ws2ipdef.h>
#include <ws2tcpip.h>
#include <wincrypt.h>

#else
#include <arpa/inet.h>
#include <netinet/in.h>
#include <pthread.h>
#include <stdint.h>
#include <sys/socket.h>
#include <unistd.h>
#endif

#include <openssl/bio.h>
#include <openssl/evp.h>
#include <openssl/pem.h>
#include <openssl/pkcs12.h>
#include <openssl/x509.h>
#include <openssl/err.h>
#include <cassert>

// MsQuic API table and shared registration with a single reference counter
static const QUIC_API_TABLE* MsQuic = NULL;
static HQUIC SharedRegistration = NULL;
static int RefCount = 0;
static CXPLAT_LOCK ResourceLock;
static int ResourceLockInitialized = 0;
#define MAXIPLEN INET6_ADDRSTRLEN
#define MAX_SEND_BUFFER_NUM 100
#define MAXBUFFERSIZE (2000*1000)
// Ring buffer structure for send operations
typedef struct BUFFER_LIST {
    CXPLAT_SLIST_ENTRY list;
    char* buffer; // The actual buffer memory
    int size;     // Total size of the buffer
    int head;     // Index where to write next
    int tail;     // Index where to read next
    int used;     // Number of bytes used
} BUFFER_LIST;

// Structure to hold receive buffer information
typedef struct RECV_BUFFER_ENTRY {
    QUIC_BUFFER* buffers;     // Direct reference to MsQuic buffers
    uint32_t bufferCount;           // Number of buffers
    uint64_t totalLength;           // Total length of all buffers
    uint64_t consumedLength;        // Amount of data consumed
    struct RECV_BUFFER_ENTRY* next; // Next entry in the queue
} RECV_BUFFER_ENTRY;

struct P2P_SOCKET_CONTEXT;
struct P2P_STREAM_CONTEXT;

typedef struct ACCEPT_CONTEXT_LIST {
    CXPLAT_SLIST_ENTRY list;
    struct P2P_SOCKET_CONTEXT* context;
} ACCEPT_CONTEXT_LIST;

// Stream event queue entry
typedef struct STREAM_EVENT_ENTRY {
    CXPLAT_LIST_ENTRY list;
    struct StreamEvent event;
} STREAM_EVENT_ENTRY;

// Stream context structure
typedef struct P2P_STREAM_CONTEXT {
    uint64_t streamId;                // Unique stream ID
    HQUIC quicStream;                 // MsQuic stream handle
    struct P2P_SOCKET_CONTEXT* socket; // Parent socket context

    enum STREAM_STATE state;          // Current stream state
    struct StreamOptions options;     // Stream configuration options

    // Callback related
    StreamEventCallback callback;     // Event callback function
    void* userData;                   // User data for callback

    // Buffer management (reuse existing logic)
    BUFFER_LIST* recvBuffer;          // Receive buffer list
    BUFFER_LIST* recvBufferEnd;       // End of receive buffer list
    BUFFER_LIST* sendList;            // Send buffer list
    BUFFER_LIST* sendListEnd;         // End of send buffer list

    // Direct read mode support
    int useDirectReadMode;            // Flag for direct read mode
    RECV_BUFFER_ENTRY* recvDirectBufferQueueHead; // Head of direct buffer queue
    RECV_BUFFER_ENTRY* recvDirectBufferQueueTail; // Tail of direct buffer queue
    int recvDirectBufferQueueSize;    // Number of entries in queue
    CXPLAT_LOCK recvDirectBufferQueueLock; // Lock for direct buffer queue

    // Current buffer being processed
    const QUIC_BUFFER* recvDirectBuffers; // Direct reference to MsQuic buffers
    uint32_t recvDirectBufferCount;   // Number of direct buffers
    uint64_t recvDirectTotalLength;   // Total length of direct buffers
    uint64_t recvDirectConsumedLength; // Amount of data consumed

    // Send mode
    int useDirectSendMode;            // Flag for send buffer mode
    QUIC_BUFFER sendQuicBuffer[MAX_SEND_BUFFER_NUM]; // QUIC buffers for sending

    // Synchronization
    CXPLAT_LOCK bufferLock;           // Lock for buffer operations
    CXPLAT_LOCK sendBufferLock;       // Lock for send buffer operations

    // Event queue for poll mode
    CXPLAT_LIST_ENTRY eventQueue;     // Event queue
    CXPLAT_LOCK eventQueueLock;       // Lock for event queue
    CXPLAT_EVENT eventAvailable;      // Event to signal data availability
    CXPLAT_EVENT recvEvent;           // Event for receive operations
    CXPLAT_EVENT sendEvent;           // Event for send operations

    // Statistics and state
    int dataAvailable;                // Flag indicating data is available
    int sendComplete;                 // Flag indicating send operation complete
    int sendCompleteBytes;            // Number of bytes sent in last operation
    int sending;                      // Number of pending send operations
    int sendingBytes;                 // Number of bytes being sent
    int lastError;                    // Last error code

    // Reference counting
    int refCount;                     // Reference count for this stream
    CXPLAT_LOCK refLock;              // Lock for reference counting

    // Hash table entry for socket's stream table
    CXPLAT_HASHTABLE_ENTRY hashEntry; // Hash table entry
} P2P_STREAM_CONTEXT;


// P2P Socket structure
typedef struct P2P_SOCKET_CONTEXT {
    enum SOCKET_MODE mode;
    enum SOCKET_TYPE type;
    HQUIC registration;
    HQUIC configuration;
    HQUIC listener;   // For server mode
    HQUIC connection; // For client mode or accepted connections
    HQUIC stream;     // Current active stream (for backward compatibility)

    // Stream management
    CXPLAT_HASHTABLE streamTable;     // Hash table for managing multiple streams
    CXPLAT_LOCK streamTableLock;      // Lock to protect stream table
    uint64_t nextStreamId;            // Next stream ID to assign
    int maxStreams;                   // Maximum number of streams allowed
    int activeStreams;                // Current number of active streams

    // Default stream for backward compatibility
    struct P2P_STREAM_CONTEXT* defaultStream; // Default stream context

    struct P2P_SOCKET_CONTEXT* parent;
    ACCEPT_CONTEXT_LIST* contextList;
    CXPLAT_EVENT acceptEvent;
    CXPLAT_LOCK acceptLock; // Lock to protect the contextList

    CXPLAT_EVENT connectEvent;
    int isClosed;
    int isAccepted;
    char localIp[MAXIPLEN];
    int localPort;
    char remoteIp[MAXIPLEN];
    int remotePort;
    int isConnected;
    int isParent;
    int connTimeout; // Connection timeout in milliseconds
    void* userData;

    // Buffer for receiving data
    BUFFER_LIST* recvBuffer;
    BUFFER_LIST* recvBufferEnd;
    int recvBufferSize;
    int recvDataSize;
    int recvDataOffset;
    CXPLAT_EVENT recvEvent;


    // Direct read mode (no copy)
    int useDirectReadMode; // Flag to indicate if using direct read mode

    // Queue for direct read mode
    RECV_BUFFER_ENTRY* recvDirectBufferQueueHead; // Head of the buffer queue
    RECV_BUFFER_ENTRY* recvDirectBufferQueueTail; // Tail of the buffer queue
    int recvDirectBufferQueueSize;         // Number of entries in the queue
    CXPLAT_LOCK recvDirectBufferQueueLock; // Lock to protect the queue

    // Send mode
    int useDirectSendMode; // Flag to indicate if using send buffer mode (1) or
    // API copy mode (0)
    BUFFER_LIST* sendList; // sendList;
    BUFFER_LIST* sendListEnd;
    QUIC_BUFFER sendQuicBuffer[MAX_SEND_BUFFER_NUM];      // QUIC buffer for sending in API copy mode
    uint64_t     workerThreadID; // ID of the worker thread for SendInline optimization

    // Flow control variables for non-useDirectSendMode (single stream)
    uint64_t bytesOutstanding;        // Bytes sent but not yet acknowledged
    uint64_t idealSendBuffer;         // Ideal send buffer size from QUIC
    uint64_t bytesSent;               // Total bytes sent
    uint64_t bytesAcked;              // Total bytes acknowledged

    CXPLAT_LOCK sendBufferLock; // Lock to protect ring buffer access
    CXPLAT_EVENT sendEvent;

    // Event handling
    int lastError;         // Last error code
    int dataAvailable;     // Flag indicating data is available to read
    int sendComplete;      // Flag indicating send operation is complete
    int sendCompleteBytes; // Number of bytes sent in last operation
    int sending;
    int sendingBytes;

    // SSL/TLS related
    const char* certPath;       // Path to certificate file (if loading from file)
    const char* privateKeyPath; // Path to private key file (if loading from file)
    const char
        * certData; // Certificate data in PEM format (if loading from memory)
    const char*
        privateKeyData; // Private key data in PEM format (if loading from memory)
    int useMemoryCert;  // Flag to indicate if using memory-based certificates
    CertVerifyCallback certVerifyCallback;

} P2P_SOCKET_CONTEXT;

// Helper structure for memory-based certificates
typedef struct CERT_MEMORY_STORE {
    void* pkcs12Data;
    uint32_t pkcs12DataLength;
    const char* pkcs12Password;
    int isValid;
} CERT_MEMORY_STORE;

// Helper function to create PKCS12 data from PEM
static PKCS12* CreatePKCS12FromPEM(const char* certPEM, const char* keyPEM,
    const char* password) {
    PKCS12* p12 = NULL;
    BIO* certBio = NULL;
    BIO* keyBio = NULL;
    X509* cert = NULL;
    EVP_PKEY* pkey = NULL;

    // Read certificate
    certBio = BIO_new_mem_buf((void*)certPEM, -1);
    if (certBio == NULL) {
        goto cleanup;
    }

    cert = PEM_read_bio_X509(certBio, NULL, NULL, NULL);
    if (cert == NULL) {
        goto cleanup;
    }

    // Read private key
    keyBio = BIO_new_mem_buf((void*)keyPEM, -1);
    if (keyBio == NULL) {
        goto cleanup;
    }

    pkey = PEM_read_bio_PrivateKey(keyBio, NULL, NULL, NULL);
    if (pkey == NULL) {
        goto cleanup;
    }

    // Create PKCS12
    p12 = PKCS12_create((char*)password, "p2psocket", pkey, cert, NULL, 0, 0, 0,
        0, 0);

cleanup:
    if (cert)
        X509_free(cert);
    if (pkey)
        EVP_PKEY_free(pkey);
    if (certBio)
        BIO_free(certBio);
    if (keyBio)
        BIO_free(keyBio);

    return p12;
}
static void* P2pMalloc(size_t len) {
    void* p = malloc(len);
    if (p)
        memset(p, 0, len);
    return p;
}
// Helper function to convert PKCS12 to DER
static void* PKCS12ToDER(PKCS12* p12, uint32_t* outLength) {
    void* derData = NULL;
    BIO* memBio = BIO_new(BIO_s_mem());
    if (memBio == NULL) {
        return NULL;
    }

    if (i2d_PKCS12_bio(memBio, p12) <= 0) {
        BIO_free(memBio);
        return NULL;
    }

    BUF_MEM* bufMem;
    BIO_get_mem_ptr(memBio, &bufMem);

    derData = malloc(bufMem->length);
    if (derData) {
        memcpy(derData, bufMem->data, bufMem->length);
        *outLength = (uint32_t)bufMem->length;
    }

    BIO_free(memBio);
    return derData;
}

// Helper function to create memory-based certificate store
static CERT_MEMORY_STORE CreateMemoryCertStore(const char* certData,
    const char* keyData) {
    CERT_MEMORY_STORE result = { 0 };

    const char* password = "p2psocket";

    PKCS12* p12 = CreatePKCS12FromPEM(certData, keyData, password);
    if (p12 == NULL) {
        return result;
    }

    result.pkcs12Data = PKCS12ToDER(p12, &result.pkcs12DataLength);
    result.pkcs12Password = password;
    result.isValid = (result.pkcs12Data != NULL);

    PKCS12_free(p12);

    return result;
}

// Helper function to free memory-based certificate store
static void FreeMemoryCertStore(CERT_MEMORY_STORE* store) {
    if (store->isValid) {
        if (store->pkcs12Data) {
            free(store->pkcs12Data);
            store->pkcs12Data = NULL;
        }
        store->isValid = 0;
    }
}

// Helper function to safely check if a pointer could be a valid X509 certificate
static int IsValidX509Pointer(void* ptr) {
    if (ptr == NULL) {
        return 0;
    }

    // Try to access X509 structure safely
    // This is a heuristic check - we'll try basic OpenSSL operations
    X509* x509 = (X509*)ptr;

    // Set up error handling to catch OpenSSL errors
    unsigned long oldError = ERR_peek_error();
    ERR_clear_error();

    // Use a more conservative approach with exception handling
    // Try to get the version which should be 0, 1, or 2 for valid X.509 certificates
    long version = -1;
    ASN1_INTEGER* serial = NULL;
    X509_NAME* subject = NULL;

    // Wrap in a try-catch equivalent using OpenSSL error checking
    version = X509_get_version(x509);
    if (ERR_peek_error() != 0) {
        ERR_clear_error();
        return 0;
    }

    if (version < 0 || version > 2) {
        ERR_clear_error();
        return 0; // Invalid version
    }

    // Try to get serial number - this should not fail for valid certificates
    serial = X509_get_serialNumber(x509);
    if (ERR_peek_error() != 0 || serial == NULL) {
        ERR_clear_error();
        return 0;
    }

    // Try to get subject name - another basic operation
    subject = X509_get_subject_name(x509);
    if (ERR_peek_error() != 0 || subject == NULL) {
        ERR_clear_error();
        return 0;
    }

    // Check if any OpenSSL errors occurred during our checks
    unsigned long newError = ERR_peek_error();
    if (newError != 0 && newError != oldError) {
        ERR_clear_error();
        return 0; // OpenSSL errors indicate invalid certificate
    }

    return 1; // Looks like a valid X509 certificate
}

// Helper function to safely check if a pointer could be a valid QUIC_PORTABLE_CERTIFICATE
static int IsValidPortableCertPointer(void* ptr) {
    if (ptr == NULL) {
        return 0;
    }

    QUIC_PORTABLE_CERTIFICATE* portableCert = (QUIC_PORTABLE_CERTIFICATE*)ptr;

    // Basic sanity checks for portable certificate structure
    if (portableCert->PortableCertificate.Buffer == NULL) {
        return 0;
    }

    if (portableCert->PortableCertificate.Length == 0 ||
        portableCert->PortableCertificate.Length > 65536) { // Reasonable max size
        return 0;
    }

    // Try to check if the buffer contains valid DER data by attempting to parse it
    const unsigned char* derData = portableCert->PortableCertificate.Buffer;
    size_t derLength = portableCert->PortableCertificate.Length;

    // Clear any existing OpenSSL errors
    ERR_clear_error();

    // Try to parse as X509 DER data
    X509* testCert = d2i_X509(NULL, &derData, derLength);
    if (testCert != NULL) {
        X509_free(testCert);
        return 1; // Valid portable certificate
    }

    // Clear any errors from the failed parse attempt
    ERR_clear_error();
    return 0;
}

// Helper function to convert DER certificate data to PEM format
static char* ConvertDERToPEM(const unsigned char* derData, size_t derLength) {
    if (derData == NULL || derLength == 0) {
        return NULL;
    }

    BIO* bio = BIO_new(BIO_s_mem());
    if (bio == NULL) {
        return NULL;
    }

    // Create X509 certificate from DER data
    X509* cert = d2i_X509(NULL, &derData, derLength);
    if (cert == NULL) {
        BIO_free(bio);
        return NULL;
    }

    // Write certificate to BIO in PEM format
    if (PEM_write_bio_X509(bio, cert) != 1) {
        X509_free(cert);
        BIO_free(bio);
        return NULL;
    }

    // Get PEM data from BIO
    BUF_MEM* bufMem;
    BIO_get_mem_ptr(bio, &bufMem);

    // Allocate memory for PEM string and copy data
    char* pemData = (char*)malloc(bufMem->length + 1);
    if (pemData != NULL) {
        memcpy(pemData, bufMem->data, bufMem->length);
        pemData[bufMem->length] = '\0';
    }

    X509_free(cert);
    BIO_free(bio);
    return pemData;
}

// Helper function to extract certificate information for verification callback
static char* ExtractCertificateInfo(QUIC_CERTIFICATE* certificate) {

    if (certificate == NULL) {
        LLog::Log(LOG_ERROR, "ExtractCertificateInfo: Certificate pointer is NULL");
        return NULL;
    }

    // For POSIX platforms, MsQuic uses OpenSSL and the certificate could be:
    // 1. X509* (OpenSSL certificate structure)
    // 2. QUIC_PORTABLE_CERTIFICATE* (portable certificate format)
    // 3. Raw DER data
    // 4. Different formats between client and server modes

    LLog::Log(LOG_INFO, "ExtractCertificateInfo: certificate pointer = %p", certificate);

    // Try to handle different certificate formats on POSIX

    // First, try to treat it as X509* (most common case with OpenSSL backend)
    // Use our safety check before attempting to access the X509 structure
    if (IsValidX509Pointer(certificate)) {
        LLog::Log(LOG_INFO, "ExtractCertificateInfo: Detected valid X509 certificate");
        X509* x509Cert = (X509*)certificate;

        // Create BIO for PEM output
        BIO* bio = BIO_new(BIO_s_mem());
        if (bio == NULL) {
            LLog::Log(LOG_ERROR, "ExtractCertificateInfo: Failed to create BIO");
            return strdup("Failed to create BIO for certificate conversion");
        }

        // Try to write X509 certificate to BIO in PEM format
        if (PEM_write_bio_X509(bio, x509Cert) == 1) {
            // Success - get PEM data from BIO
            BUF_MEM* bufMem;
            BIO_get_mem_ptr(bio, &bufMem);

            // Allocate memory for PEM string and copy data
            char* pemData = (char*)malloc(bufMem->length + 1);
            if (pemData != NULL) {
                memcpy(pemData, bufMem->data, bufMem->length);
                pemData[bufMem->length] = '\0';
                LLog::Log(LOG_INFO, "ExtractCertificateInfo: Successfully extracted X509 certificate as PEM");
            }

            BIO_free(bio);
            return pemData ? pemData : strdup("Failed to allocate memory for PEM data");
        } else {
            LLog::Log(LOG_ERROR, "ExtractCertificateInfo: PEM_write_bio_X509 failed");
        }

        BIO_free(bio);
    } else {
        LLog::Log(LOG_INFO, "ExtractCertificateInfo: Certificate is not a valid X509 pointer");
    }

    // If X509* approach failed, try to handle as QUIC_PORTABLE_CERTIFICATE
    // Use our safer validation function
    if (IsValidPortableCertPointer(certificate)) {
        LLog::Log(LOG_INFO, "ExtractCertificateInfo: Detected valid portable certificate format");

        QUIC_PORTABLE_CERTIFICATE* portableCert = (QUIC_PORTABLE_CERTIFICATE*)certificate;
        LLog::Log(LOG_INFO, "ExtractCertificateInfo: Portable certificate length = %u",
                  portableCert->PortableCertificate.Length);

        // The portable certificate should contain DER data
        const unsigned char* derData = portableCert->PortableCertificate.Buffer;
        size_t derLength = portableCert->PortableCertificate.Length;

        // Convert DER to PEM format
        char* pemData = ConvertDERToPEM(derData, derLength);
        if (pemData != NULL) {
            LLog::Log(LOG_INFO, "ExtractCertificateInfo: Successfully extracted portable certificate as PEM");
            return pemData;
        } else {
            LLog::Log(LOG_ERROR, "ExtractCertificateInfo: Failed to convert portable certificate DER to PEM");
        }
    } else {
        LLog::Log(LOG_INFO, "ExtractCertificateInfo: Certificate is not a valid portable certificate");
    }

    // Additional approach: Check if this might be a different certificate structure
    // that we haven't accounted for. Log some diagnostic information.
    LLog::Log(LOG_INFO, "ExtractCertificateInfo: Attempting to analyze unknown certificate format");

    // Try to read the first few bytes to see if it looks like DER data
    const unsigned char* testPtr = (const unsigned char*)certificate;
    if (testPtr != NULL) {
        // DER certificates typically start with 0x30 (SEQUENCE tag)
        LLog::Log(LOG_INFO, "ExtractCertificateInfo: First 4 bytes: %02x %02x %02x %02x",
                  testPtr[0], testPtr[1], testPtr[2], testPtr[3]);

        // If it starts with 0x30, it might be DER data
        if (testPtr[0] == 0x30) {
            LLog::Log(LOG_INFO, "ExtractCertificateInfo: Data appears to start with DER SEQUENCE tag");

            // Try to extract length from DER encoding
            size_t derLength = 0;
            if (testPtr[1] & 0x80) {
                // Long form length
                int lengthBytes = testPtr[1] & 0x7F;
                if (lengthBytes <= 4 && lengthBytes > 0) {
                    for (int i = 0; i < lengthBytes; i++) {
                        derLength = (derLength << 8) | testPtr[2 + i];
                    }
                    derLength += 2 + lengthBytes; // Add header bytes

                    if (derLength > 0 && derLength < 65536) { // Reasonable size
                        LLog::Log(LOG_INFO, "ExtractCertificateInfo: Attempting to parse as DER with length %zu", derLength);
                        char* pemData = ConvertDERToPEM(testPtr, derLength);
                        if (pemData != NULL) {
                            LLog::Log(LOG_INFO, "ExtractCertificateInfo: Successfully extracted DER data as PEM");
                            return pemData;
                        }
                    }
                }
            } else {
                // Short form length
                derLength = testPtr[1] + 2; // Add header bytes
                if (derLength > 0 && derLength < 65536) { // Reasonable size
                    LLog::Log(LOG_INFO, "ExtractCertificateInfo: Attempting to parse as DER with short length %zu", derLength);
                    char* pemData = ConvertDERToPEM(testPtr, derLength);
                    if (pemData != NULL) {
                        LLog::Log(LOG_INFO, "ExtractCertificateInfo: Successfully extracted DER data as PEM");
                        return pemData;
                    }
                }
            }
        }
    }

    LLog::Log(LOG_ERROR, "ExtractCertificateInfo: All certificate extraction methods failed");

    // If all approaches failed, return error message
    return strdup("Failed to extract certificate data on POSIX platform - unsupported certificate format");
}

static BUFFER_LIST* BufferListAppend(BUFFER_LIST* list, const char* data, int len) {
    BUFFER_LIST* b = (BUFFER_LIST*)P2pMalloc(sizeof(BUFFER_LIST) + len);
    if (b == NULL)
        return NULL;
    b->buffer = ((char*)b) + sizeof(BUFFER_LIST);
    if (list)
        list->list.Next = (CXPLAT_SLIST_ENTRY*)b;
    b->list.Next = NULL;
    memcpy(b->buffer, data, len);
    b->size = len;
    //LLog::Log(LOG_INFO, "malloc buffer %p %d", b, len);
    return b;
}
static BUFFER_LIST* BufferListAppend(BUFFER_LIST* list, struct p2p_iovec* iov, int count) {
    size_t total = 0;
    for (int i = 0; i < count; i++) {
        total += iov[i].iov_len;
    }
    BUFFER_LIST* b = (BUFFER_LIST*)P2pMalloc(sizeof(BUFFER_LIST) + total);
    if (b == NULL)
        return NULL;
    b->buffer = ((char*)b) + sizeof(BUFFER_LIST);
    if (list)
        list->list.Next = (CXPLAT_SLIST_ENTRY*)b;
    b->list.Next = NULL;

    total = 0;
    for (int i = 0; i < count; i++) {
        memcpy(&b->buffer[total], iov[i].iov_base, iov[i].iov_len);
        total += iov[i].iov_len;
    }

    b->size = total;
    //LLog::Log(LOG_INFO, "malloc buffer %p %d", b,
    //    total);
    return b;
}

// Initialize a ring buffer with a given size
static int RingBufferInit(BUFFER_LIST* rb, int size) {
    rb->buffer = (char*)malloc(size);
    if (rb->buffer == NULL) {
        return -1;
    }
    rb->size = size;
    rb->head = 0;
    rb->tail = 0;
    rb->used = 0;
    return 0;
}

// Free a ring buffer
static void BufferListFree(BUFFER_LIST* rb) {
    if (rb->buffer != NULL) {
        //free(rb->buffer);
        rb->buffer = NULL;
    }
    //LLog::Log(LOG_INFO, "free buffer %p %d", rb,
    //    rb->size);
    rb->size = 0;
    rb->head = 0;
    rb->tail = 0;
    rb->used = 0;
    free(rb);
}

// Write data to a ring buffer
static int RingBufferWrite(BUFFER_LIST* rb, const char* data, int len) {
    if (rb->buffer == NULL || len <= 0) {
        return -1;
    }

    // Check if there's enough space
    if (rb->used + len > rb->size) {
        // Need to resize the buffer
        int newSize = rb->size * 2;
        if (newSize < rb->used + len) {
            newSize = rb->used + len;
        }

        // Allocate new buffer
        char* newBuffer = (char*)malloc(newSize);
        if (newBuffer == NULL) {
            return -1;
        }

        // Copy existing data to new buffer
        if (rb->used > 0) {
            if (rb->tail < rb->head) {
                // Data is contiguous
                memcpy(newBuffer, rb->buffer + rb->tail, rb->used);
            }
            else {
                // Data is wrapped around
                int firstPart = rb->size - rb->tail;
                memcpy(newBuffer, rb->buffer + rb->tail, firstPart);
                memcpy(newBuffer + firstPart, rb->buffer, rb->head);
            }
        }

        // Update buffer pointers
        free(rb->buffer);
        rb->buffer = newBuffer;
        rb->size = newSize;
        rb->tail = 0;
        rb->head = rb->used;
    }

    // Write data to buffer
    if (rb->head + len <= rb->size) {
        // Data fits without wrapping
        memcpy(rb->buffer + rb->head, data, len);
        rb->head = (rb->head + len) % rb->size;
    }
    else {
        // Data needs to wrap around
        int firstPart = rb->size - rb->head;
        memcpy(rb->buffer + rb->head, data, firstPart);
        memcpy(rb->buffer, data + firstPart, len - firstPart);
        rb->head = len - firstPart;
    }

    rb->used += len;
    return len;
}

// Read data from a ring buffer without removing it
static int RingBufferPeek(BUFFER_LIST* rb, char* data, int len) {
    if (rb->buffer == NULL || rb->used == 0 || len <= 0) {
        return 0;
    }

    // Limit read to available data
    if (len > rb->used) {
        len = rb->used;
    }

    // Read data from buffer
    if (rb->tail + len <= rb->size) {
        // Data is contiguous
        memcpy(data, rb->buffer + rb->tail, len);
    }
    else {
        // Data is wrapped around
        int firstPart = rb->size - rb->tail;
        memcpy(data, rb->buffer + rb->tail, firstPart);
        memcpy(data + firstPart, rb->buffer, len - firstPart);
    }

    return len;
}

// Consume data from a ring buffer (advance the tail)
static void RingBufferConsume(BUFFER_LIST* rb, int len) {
    if (rb->buffer == NULL || rb->used == 0 || len <= 0) {
        return;
    }

    // Limit consumption to available data
    if (len > rb->used) {
        len = rb->used;
    }

    rb->tail = (rb->tail + len) % rb->size;
    rb->used -= len;

    // Reset pointers if buffer is empty
    if (rb->used == 0) {
        rb->head = 0;
        rb->tail = 0;
    }
}

// Forward declarations for callback handlers
QUIC_STATUS
ClientConnectionCallback(HQUIC Connection, void* Context,
    QUIC_CONNECTION_EVENT* Event);

QUIC_STATUS
ServerConnectionCallback(HQUIC Connection, void* Context,
    QUIC_CONNECTION_EVENT* Event);

QUIC_STATUS
StreamCallback(HQUIC Stream, void* Context, QUIC_STREAM_EVENT* Event);

QUIC_STATUS
NewStreamCallback(HQUIC Stream, void* Context, QUIC_STREAM_EVENT* Event);

QUIC_STATUS
ListenerCallback(HQUIC Listener, void* Context, QUIC_LISTENER_EVENT* Event);

// Stream management helper functions
static P2P_STREAM_CONTEXT* CreateStreamContext(P2P_SOCKET_CONTEXT* socketContext, struct StreamOptions* options);
static void DestroyStreamContext(P2P_STREAM_CONTEXT* streamContext);
static void AddStreamToSocket(P2P_SOCKET_CONTEXT* socketContext, P2P_STREAM_CONTEXT* streamContext);
static void RemoveStreamFromSocket(P2P_SOCKET_CONTEXT* socketContext, P2P_STREAM_CONTEXT* streamContext);
static P2P_STREAM_CONTEXT* FindStreamById(P2P_SOCKET_CONTEXT* socketContext, uint64_t streamId);
static void StreamAddRef(P2P_STREAM_CONTEXT* streamContext);
static void StreamRelease(P2P_STREAM_CONTEXT* streamContext);
static void QueueStreamEvent(P2P_STREAM_CONTEXT* streamContext, struct StreamEvent* event);
static int DequeueStreamEvent(P2P_STREAM_CONTEXT* streamContext, struct StreamEvent* event);

// Hash table management is now handled by MsQuic hashtable API

// Helper function to send data from socket's buffer list with flow control (single stream)
static int SendDataFromSocketBufferList(P2P_SOCKET_CONTEXT* context) {
    if (context == NULL || context->stream == NULL) {
        return -1;
    }

    // Check flow control - only send if we have room in the ideal buffer
    if (context->bytesOutstanding >= context->idealSendBuffer) {
        return 0; // No room to send more data
    }

    QUIC_STATUS status;
    QUIC_SEND_FLAGS sendFlags = QUIC_SEND_FLAG_NONE;

    // Acquire the lock to protect buffer access
    CxPlatLockAcquire(&context->sendBufferLock);

    // If there's no data to send or a send is already in progress, return
    if (context->sendList == NULL || context->sendQuicBuffer[0].Buffer) {
        CxPlatLockRelease(&context->sendBufferLock);
        return 0;
    }

    // Calculate how much we can send based on flow control
    uint64_t availableBuffer = context->idealSendBuffer - context->bytesOutstanding;

    auto list = context->sendList;
    uint64_t totalSendSize = 0;
    int bufferCount = 0;

    // Count how many buffers we can send within the flow control limit
    while (list != NULL && totalSendSize < availableBuffer) {
        uint64_t nextSize = totalSendSize + list->size;
        if (nextSize > availableBuffer) {
            break; // Don't send more than we have room for
        }

        context->sendQuicBuffer[bufferCount].Buffer = (uint8_t*)list->buffer;
        context->sendQuicBuffer[bufferCount].Length = list->size;
        totalSendSize += list->size;
        bufferCount++;
        list = (BUFFER_LIST*)list->list.Next;

    }
    LLog::Log(LOG_INFO, "Sending %d buffers of total size %d bytes availableBuffer %d context->idealSendBuffer %d context->bytesOutstanding %d \n", bufferCount, totalSendSize,availableBuffer,context->idealSendBuffer,context->bytesOutstanding);

    if (bufferCount == 0 || totalSendSize == 0) {
        CxPlatLockRelease(&context->sendBufferLock);
        return 0; // Nothing to send due to flow control
    }

    // Update flow control counters
    context->bytesOutstanding += totalSendSize;
    context->bytesSent += totalSendSize;

    // Release the lock before calling StreamSend to avoid potential deadlocks
    CxPlatLockRelease(&context->sendBufferLock);

    // Send data
    status = MsQuic->StreamSend(context->stream, context->sendQuicBuffer,
                               bufferCount, sendFlags, context->sendQuicBuffer);
    if (QUIC_FAILED(status)) {
        // Rollback flow control counters on failure
        CxPlatLockAcquire(&context->sendBufferLock);
        context->bytesOutstanding -= totalSendSize;
        context->bytesSent -= totalSendSize;
        CxPlatLockRelease(&context->sendBufferLock);

        memset(context->sendQuicBuffer, 0, sizeof(context->sendQuicBuffer));
        return -1;
    }

    return (int)totalSendSize;
}

// Helper function to send data from the ring buffer
static int SendDataFromBufferList(void* soc) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL || context->stream == NULL || !context->isConnected || context->isClosed) {
        return -1;
    }

    int sendLen = 0;
    int num = MAX_SEND_BUFFER_NUM;
    QUIC_STATUS status;
    QUIC_SEND_FLAGS sendFlags = QUIC_SEND_FLAG_NONE;
    BOOLEAN sendInline;

    // Acquire the lock to protect ring buffer access
    CxPlatLockAcquire(&context->sendBufferLock);

    // If there's no data to send or a send is already in progress, return
    if (context->sendList == NULL || context->sendQuicBuffer[0].Buffer) {
        CxPlatLockRelease(&context->sendBufferLock);
        return 0;
    }
    if (num > context->sending)
        num = context->sending;
    auto list = context->sendList;
    for (int i = 0; i < num; i++) {
        if (list == NULL) {
            LLog::Log(LOG_ERROR, "no data");
            break;
        }
        context->sendQuicBuffer[i].Buffer = (uint8_t*)list->buffer;
        context->sendQuicBuffer[i].Length = list->size;
        sendLen += list->size;
        list = (BUFFER_LIST*)list->list.Next;
    }

    // Release the lock before calling StreamSend to avoid potential deadlocks
    CxPlatLockRelease(&context->sendBufferLock);

    // Send data
    status = MsQuic->StreamSend(context->stream, context->sendQuicBuffer, num,
        sendFlags, context->sendQuicBuffer);
    if (QUIC_FAILED(status)) {
        memset(context->sendQuicBuffer, 0, sizeof(context->sendQuicBuffer));
        return -1;
    }

    return sendLen;
}

// Thread-safe initialization of a lock
static void EnsureLockInitialized(CXPLAT_LOCK* lock, int* initialized) {
    // Use atomic compare-and-swap to ensure only one thread initializes the lock
#ifdef _WIN32
    if (InterlockedCompareExchange((LONG*)initialized, 1, 0) == 0) {
        CxPlatLockInitialize(lock);
    }
#else
  // For non-Windows platforms, use a simple approach
  // This isn't fully thread-safe but is better than nothing
    if (*initialized == 0) {
        CxPlatLockInitialize(lock);
        *initialized = 1;
    }
#endif
}

// Initialize MsQuic and shared registration with a single reference counter
static HQUIC InitializeSharedResources() {
    // Ensure the lock is initialized before using it
    EnsureLockInitialized(&ResourceLock, &ResourceLockInitialized);

    // Acquire the lock to protect shared resources access
    CxPlatLockAcquire(&ResourceLock);

    // Check if resources are already initialized
    if (MsQuic != NULL && SharedRegistration != NULL) {
        // Resources already initialized, just increment the reference count
        RefCount++;
        HQUIC result = SharedRegistration;
        CxPlatLockRelease(&ResourceLock);
        return result;
    }

    // Initialize MsQuic if needed
    if (MsQuic == NULL) {
        QUIC_STATUS status = MsQuicOpen2(&MsQuic);
        if (QUIC_FAILED(status)) {
            CxPlatLockRelease(&ResourceLock);
            return NULL;
        }
    }

    // Initialize registration if needed
    if (SharedRegistration == NULL) {
        QUIC_REGISTRATION_CONFIG RegConfig = { 0 };
        RegConfig.AppName = "p2psocket";
        RegConfig.ExecutionProfile = QUIC_EXECUTION_PROFILE_TYPE_MAX_THROUGHPUT;

        QUIC_STATUS status =
            MsQuic->RegistrationOpen(&RegConfig, &SharedRegistration);
        if (QUIC_FAILED(status)) {
            // If registration fails, clean up MsQuic
            if (RefCount == 0) {
                MsQuicClose(MsQuic);
                MsQuic = NULL;
            }
            CxPlatLockRelease(&ResourceLock);
            return NULL;
        }
    }

    // Increment reference count
    RefCount++;

    HQUIC result = SharedRegistration;
    CxPlatLockRelease(&ResourceLock);
    return result;
}

// Uninitialize MsQuic and shared registration with a single reference counter
static void UninitializeSharedResources() {
    // Ensure the lock is initialized before using it
    EnsureLockInitialized(&ResourceLock, &ResourceLockInitialized);

    // Acquire the lock to protect shared resources access
    CxPlatLockAcquire(&ResourceLock);

    if (RefCount > 0) {
        RefCount--;

        if (RefCount == 0) {
            LLog::Log(LOG_INFO, "UninitializeSharedResources");
            // Last reference, clean up resources
            if (SharedRegistration != NULL) {
                MsQuic->RegistrationClose(SharedRegistration);
                SharedRegistration = NULL;
            }

            if (MsQuic != NULL) {
                LLog::Log(LOG_INFO, "MsQuicClose");
                MsQuicClose(MsQuic);
                MsQuic = NULL;
            }
            LLog::Log(LOG_INFO, "UninitializeSharedResources successful");
            // Note: We don't uninitialize the lock here because it's static
            // and might be used again in future calls
        }
    }

    CxPlatLockRelease(&ResourceLock);
}

// Create a new P2P socket
P2P_SOCKET QuicCreate(SocketOptions* option) {
    // Get the shared resources
    HQUIC registration = InitializeSharedResources();
    if (registration == NULL) {
        return NULL;
    }

    P2P_SOCKET_CONTEXT* context =
        (P2P_SOCKET_CONTEXT*)P2pMalloc(sizeof(P2P_SOCKET_CONTEXT));
    if (context == NULL) {
        UninitializeSharedResources(); // Release the resources reference
        return NULL;
    }

    // Initialize context with options
    context->mode = option->mode;
    context->type = option->type;
    context->connTimeout = 10000; // Default 10 seconds
    if (context->mode == MODE_SERVER) {
        context->isParent = 1;
        CxPlatLockInitialize(&context->acceptLock);
        CxPlatEventInitialize(&context->acceptEvent, TRUE, FALSE);
    }
    else
    {
        CxPlatEventInitialize(&context->connectEvent, TRUE, FALSE);
    }

    // Initialize event handling flags
    context->lastError = 0;
    context->dataAvailable = 0;
    context->sendComplete = 0;
    context->sendCompleteBytes = 0;
    CxPlatEventInitialize(&context->sendEvent, TRUE, FALSE);

    // Initialize direct read mode fields
    context->useDirectReadMode = 0; // Default to copy mode

    // Initialize buffer queue (only used in direct read mode)
    context->recvDirectBufferQueueHead = NULL;
    context->recvDirectBufferQueueTail = NULL;
    context->recvDirectBufferQueueSize = 0;
    CxPlatLockInitialize(&context->recvDirectBufferQueueLock);

    // Initialize send mode fields
    context->useDirectSendMode = 1; // Default to API copy mode
    context->workerThreadID = 0;    // Will be set when connection is established

    // Initialize flow control variables for single stream
    context->bytesOutstanding = 0;
    context->idealSendBuffer = 2 * 1024 * 1024; // Default 64KB, will be updated by QUIC
    context->bytesSent = 0;
    context->bytesAcked = 0;

    memset(&context->sendQuicBuffer, 0, sizeof(QUIC_BUFFER));

    // Initialize the lock for thread safety
    CxPlatLockInitialize(&context->sendBufferLock);

    // Handle certificate and private key (either as file paths or memory strings)
    context->useMemoryCert = 0; // Default to file-based certificates

    if (option->cert != NULL) {
        // Check if the certificate string looks like a PEM certificate
        if (strstr(option->cert, "-----BEGIN CERTIFICATE-----") != NULL) {
            context->certData = strdup(option->cert);
            context->useMemoryCert = 1; // Using memory-based certificates
        }
        else {
            context->certPath = strdup(option->cert);
        }
    }

    if (option->privatekey != NULL) {
        // Check if the private key string looks like a PEM private key
        if (strstr(option->privatekey, "-----BEGIN PRIVATE KEY-----") != NULL ||
            strstr(option->privatekey, "-----BEGIN RSA PRIVATE KEY-----") != NULL ||
            strstr(option->privatekey, "-----BEGIN EC PRIVATE KEY-----") != NULL) {
            context->privateKeyData = strdup(option->privatekey);
            context->useMemoryCert = 1; // Using memory-based certificates
        }
        else {
            context->privateKeyPath = strdup(option->privatekey);
        }
    }

    context->certVerifyCallback = option->cert_verify;

    // Use the shared registration
    context->registration = registration;

    // Initialize stream management
    CXPLAT_HASHTABLE* streamTablePtr = &context->streamTable;
    CxPlatHashtableInitialize(&streamTablePtr, CXPLAT_HASH_MIN_SIZE);
    CxPlatLockInitialize(&context->streamTableLock);
    context->nextStreamId = 0;
    context->maxStreams = 100; // Default maximum streams
    context->activeStreams = 0;
    context->defaultStream = NULL;

    // Allocate receive buffer
    context->recvBufferSize = 0; // Default buffer size
    context->recvBuffer = NULL;
    CxPlatEventInitialize(&context->recvEvent, TRUE, FALSE);

    return (P2P_SOCKET)context;
}

// Set connection timeout
int QuicSetConnTimeout(P2P_SOCKET soc, int timeout) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL) {
        return -1;
    }

    context->connTimeout = timeout;
    return 0;
}

// Bind socket to local address and port
int QuicBind(P2P_SOCKET soc, const char* ipaddr, int port) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL) {
        return -1;
    }

#ifdef _WIN32
    strcpy_s(context->localIp, MAXIPLEN, ipaddr);
#else
    int len = strlen(ipaddr);
    if (len > MAXIPLEN)

        memcpy(context->localIp, ipaddr, len);
#endif
    context->localPort = port;

    return 0;
}

// Listen for incoming connections
int QuicListen(P2P_SOCKET soc) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL || context->mode != MODE_SERVER) {
        return -1;
    }

    // Create server configuration
    QUIC_SETTINGS Settings = { 0 };
    Settings.IsSet.IdleTimeoutMs = TRUE;
    Settings.IdleTimeoutMs = 30000; // 30 seconds
    Settings.IsSet.ServerResumptionLevel = TRUE;
    Settings.ServerResumptionLevel = QUIC_SERVER_RESUME_AND_ZERORTT;

    // Set SendBufferingEnabled based on our send mode
    Settings.IsSet.SendBufferingEnabled = TRUE;
    Settings.SendBufferingEnabled =
        context->useDirectSendMode; // TRUE for API copy mode, FALSE for zero
    // copy mode

    Settings.IsSet.PeerBidiStreamCount = TRUE;
    Settings.PeerBidiStreamCount = 100;
    Settings.IsSet.PeerUnidiStreamCount = TRUE;
    Settings.PeerUnidiStreamCount = 100;

    // Create ALPN buffer
    const char* Alpn = "perf";
    QUIC_BUFFER AlpnBuffer = { (uint32_t)strlen(Alpn), (uint8_t*)Alpn };

    // Create credential configuration
    QUIC_CREDENTIAL_CONFIG CredConfig = {};
    QUIC_CERTIFICATE_FILE CertFile = { 0 };

    // For memory-based certificates
    CERT_MEMORY_STORE memoryStore = { 0 };
    QUIC_CERTIFICATE_PKCS12 Pkcs12 = { 0 };

    if (context->useMemoryCert && context->certData != NULL &&
        context->privateKeyData != NULL) {
        // Create memory-based certificate store
        memoryStore =
            CreateMemoryCertStore(context->certData, context->privateKeyData);

        if (memoryStore.isValid) {
            // 统一使用 PKCS12 方式处理证书
            Pkcs12.Asn1Blob = (const uint8_t*)memoryStore.pkcs12Data;
            Pkcs12.Asn1BlobLength = memoryStore.pkcs12DataLength;
            Pkcs12.PrivateKeyPassword = memoryStore.pkcs12Password;

            CredConfig.Type = QUIC_CREDENTIAL_TYPE_CERTIFICATE_PKCS12;
            CredConfig.CertificatePkcs12 = &Pkcs12;
        }
        else {
            // Failed to create memory-based certificate store
            CredConfig.Type = QUIC_CREDENTIAL_TYPE_NONE;
            CredConfig.Flags = QUIC_CREDENTIAL_FLAG_NO_CERTIFICATE_VALIDATION;
        }
    }
    else if (context->certPath != NULL && context->privateKeyPath != NULL) {
        // Using file-based certificates
        CertFile.CertificateFile = context->certPath;
        CertFile.PrivateKeyFile = context->privateKeyPath;
        CredConfig.Type = QUIC_CREDENTIAL_TYPE_CERTIFICATE_FILE;
        CredConfig.CertificateFile = &CertFile;
    }
    else {
        CredConfig.Type = QUIC_CREDENTIAL_TYPE_NONE;
        CredConfig.Flags = QUIC_CREDENTIAL_FLAG_NO_CERTIFICATE_VALIDATION;
    }

    // Enable certificate received notification for custom validation
    CredConfig.Flags |= QUIC_CREDENTIAL_FLAG_INDICATE_CERTIFICATE_RECEIVED;

    // If there is a certificate verification callback, disable system default
    // validation
    if (context->certVerifyCallback != NULL) {
        CredConfig.Flags |= QUIC_CREDENTIAL_FLAG_NO_CERTIFICATE_VALIDATION;
    }

    // Open configuration
    QUIC_STATUS status = MsQuic->ConfigurationOpen(
        context->registration, &AlpnBuffer, 1, &Settings, sizeof(Settings), NULL,
        &context->configuration);

    if (QUIC_FAILED(status)) {
        return -1;
    }

    // Load credentials
    status =
        MsQuic->ConfigurationLoadCredential(context->configuration, &CredConfig);

    // Free memory-based certificate store if it was created
    if (memoryStore.isValid) {
        FreeMemoryCertStore(&memoryStore);
    }

    if (QUIC_FAILED(status)) {
        MsQuic->ConfigurationClose(context->configuration);
        context->configuration = NULL;
        return -1;
    }

    // Create listener
    status = MsQuic->ListenerOpen(context->registration, ListenerCallback,
        context, &context->listener);

    if (QUIC_FAILED(status)) {
        MsQuic->ConfigurationClose(context->configuration);
        context->configuration = NULL;
        return -1;
    }

    // Create address for listener
    QUIC_ADDR Address = { 0 };
    if (strcmp(context->localIp, "0.0.0.0") != 0) {
        // Convert IP string to QUIC_ADDR
        struct sockaddr_in* addr4 = (struct sockaddr_in*)&Address;
        addr4->sin_family = AF_INET;
        inet_pton(AF_INET, context->localIp, &addr4->sin_addr);
    }

    // Set port
    QuicAddrSetPort(&Address, (uint16_t)context->localPort);

    // Start listener
    status = MsQuic->ListenerStart(context->listener, &AlpnBuffer, 1, &Address);

    if (QUIC_FAILED(status)) {
        MsQuic->ListenerClose(context->listener);
        context->listener = NULL;
        MsQuic->ConfigurationClose(context->configuration);
        context->configuration = NULL;
        return -1;
    }

    return 0;
}

// Connect to a remote server
int QuicConnect(P2P_SOCKET soc, const char* ipaddr, int port) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL || context->mode != MODE_CLIENT) {
        return -1;
    }
    LLog::Log(LOG_INFO, "QuicConnect %s %d", ipaddr, port);
#ifdef _WIN32
    strcpy_s(context->remoteIp, MAXIPLEN, ipaddr);
#else
    int len = strlen(ipaddr);
    if (len > MAXIPLEN)
        len = MAXIPLEN;
    memcpy(context->remoteIp, ipaddr, len);
#endif

    context->remotePort = port;

    // Create client configuration
    QUIC_SETTINGS Settings = { 0 };
    Settings.IsSet.IdleTimeoutMs = TRUE;
    Settings.IdleTimeoutMs = 30000; // 30 seconds

    // Set SendBufferingEnabled based on our send mode
    Settings.IsSet.SendBufferingEnabled = TRUE;
    Settings.SendBufferingEnabled =
        context->useDirectSendMode; // TRUE for API copy mode, FALSE for zero
    // copy mode

// Create ALPN buffer
    const char* Alpn = "perf";
    QUIC_BUFFER AlpnBuffer = { (uint32_t)strlen(Alpn),  (uint8_t*)Alpn };

    // Create credential configuration
    QUIC_CREDENTIAL_CONFIG CredConfig = {};
    QUIC_CERTIFICATE_FILE CertFile = { 0 };

    // For memory-based certificates
    CERT_MEMORY_STORE memoryStore = { 0 };
    QUIC_CERTIFICATE_PKCS12 Pkcs12 = { 0 };

    if (context->useMemoryCert && context->certData != NULL &&
        context->privateKeyData != NULL) {
        // Create memory-based certificate store
        memoryStore =
            CreateMemoryCertStore(context->certData, context->privateKeyData);

        if (memoryStore.isValid) {
            Pkcs12.Asn1Blob = (const uint8_t*)memoryStore.pkcs12Data;
            Pkcs12.Asn1BlobLength = memoryStore.pkcs12DataLength;
            Pkcs12.PrivateKeyPassword = memoryStore.pkcs12Password;

            CredConfig.Type = QUIC_CREDENTIAL_TYPE_CERTIFICATE_PKCS12;
            CredConfig.CertificatePkcs12 = &Pkcs12;
        }
        else {
            // Failed to create memory-based certificate store
            CredConfig.Type = QUIC_CREDENTIAL_TYPE_NONE;
            CredConfig.Flags = QUIC_CREDENTIAL_FLAG_NO_CERTIFICATE_VALIDATION;
        }
    }
    else if (context->certPath != NULL && context->privateKeyPath != NULL) {
        // Using file-based certificates
        CertFile.CertificateFile = context->certPath;
        CertFile.PrivateKeyFile = context->privateKeyPath;
        CredConfig.Type = QUIC_CREDENTIAL_TYPE_CERTIFICATE_FILE;
        CredConfig.CertificateFile = &CertFile;
    }
    else {
        CredConfig.Type = QUIC_CREDENTIAL_TYPE_NONE;
        CredConfig.Flags = QUIC_CREDENTIAL_FLAG_NO_CERTIFICATE_VALIDATION;
    }

    // Enable certificate received notification for custom validation
    CredConfig.Flags |= QUIC_CREDENTIAL_FLAG_INDICATE_CERTIFICATE_RECEIVED;
    CredConfig.Flags |= QUIC_CREDENTIAL_FLAG_CLIENT;
    // If there is a certificate verification callback, disable system default
    // validation
    if (context->certVerifyCallback != NULL) {
        CredConfig.Flags |= QUIC_CREDENTIAL_FLAG_NO_CERTIFICATE_VALIDATION;
    }

    // Open configuration
    QUIC_STATUS status = MsQuic->ConfigurationOpen(
        context->registration, &AlpnBuffer, 1, &Settings, sizeof(Settings), NULL,
        &context->configuration);

    if (QUIC_FAILED(status)) {
        LLog::Log(LOG_ERROR, "ConfigurationOpen error");
        return -1;
    }

    // Load credentials
    status =
        MsQuic->ConfigurationLoadCredential(context->configuration, &CredConfig);
    // QUIC_CREDENTIAL_CONFIG cli_CredConfig;
    // memset(&cli_CredConfig, 0, sizeof(QUIC_CREDENTIAL_CONFIG));
    // cli_CredConfig.Flags = QUIC_CREDENTIAL_FLAG_CLIENT |
    //    QUIC_CREDENTIAL_FLAG_NO_CERTIFICATE_VALIDATION;

    status =
        MsQuic->ConfigurationLoadCredential(context->configuration, &CredConfig);

    // Free memory-based certificate store if it was created
    if (memoryStore.isValid) {
        FreeMemoryCertStore(&memoryStore);
    }

    if (QUIC_FAILED(status)) {
        MsQuic->ConfigurationClose(context->configuration);
        context->configuration = NULL;
        return -1;
    }
    CxPlatEventReset(context->connectEvent);
    // Create connection
    status =
        MsQuic->ConnectionOpen(context->registration, ClientConnectionCallback,
            context, &context->connection);

    if (QUIC_FAILED(status)) {
        MsQuic->ConfigurationClose(context->configuration);
        context->configuration = NULL;
        return -1;
    }

    // Start connection
    status = MsQuic->ConnectionStart(
        context->connection, context->configuration, QUIC_ADDRESS_FAMILY_UNSPEC,
        context->remoteIp, (uint16_t)context->remotePort);

    if (QUIC_FAILED(status)) {
        MsQuic->ConnectionClose(context->connection);
        context->connection = NULL;
        MsQuic->ConfigurationClose(context->configuration);
        context->configuration = NULL;
        LLog::Log(LOG_ERROR, "ConnectionStart error %s %d", context->remoteIp, context->remotePort);
        return -1;
    }

    CxPlatEventWaitWithTimeout(context->connectEvent, context->connTimeout);

    return context->isConnected ? 0 : -1;
}

// Write data to the socket
int QuicWrite(P2P_SOCKET soc, const char* buffer, int len) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL || context->connection == NULL || !context->isConnected) {
        return -1;
    }

    // Create stream if not already created
    if (context->stream == NULL) {
        QUIC_STATUS status =
            MsQuic->StreamOpen(context->connection, QUIC_STREAM_OPEN_FLAG_NONE,
                StreamCallback, context, &context->stream);

        if (QUIC_FAILED(status)) {
            return -1;
        }

        status = MsQuic->StreamStart(context->stream, QUIC_STREAM_START_FLAG_NONE);

        if (QUIC_FAILED(status)) {
            MsQuic->StreamClose(context->stream);
            context->stream = NULL;
            return -1;
        }
    }
    LLog::Log(LOG_INFO, "quic wirte %x %d", soc, len);
    CxPlatEventReset(context->sendEvent);
    if (context->useDirectSendMode) {
        QUIC_SEND_FLAGS sendFlags = QUIC_SEND_FLAG_NONE;

        // Create buffer for sending

        context->sendQuicBuffer[0] = { (uint32_t)len,  (uint8_t*)buffer };
        CxPlatLockAcquire(&context->sendBufferLock);
        context->sending++;
        context->sendingBytes += len;
        CxPlatLockRelease(&context->sendBufferLock);
        // Send data
        QUIC_STATUS status = MsQuic->StreamSend(context->stream, context->sendQuicBuffer, 1,
            sendFlags, context->sendQuicBuffer);

        if (QUIC_FAILED(status)) {
            CxPlatLockAcquire(&context->sendBufferLock);
            context->sending--;
            context->sendingBytes -= len;
            CxPlatLockRelease(&context->sendBufferLock);
            return -1;
        }

        // Wait for send completion in blocking mode
        context->sendComplete = 0;
        context->sendCompleteBytes = 0;

        // Wait for send completion or timeout
        int timeout = context->connTimeout;
        auto ret = CxPlatEventWaitWithTimeout(context->sendEvent, context->connTimeout);
        if (!ret) {
            LLog::Log(LOG_INFO, "soc %p, send timeout  %d", context,
                context->connTimeout);
            return -1;
        }
        LLog::Log(LOG_INFO, "recv sended notify %d and return %d", len);

        // If send completed, return the number of bytes sent
        if (context->sending) {
            LLog::Log(LOG_ERROR, "sending error");
        }

        // Timeout but request was successfully submitted, return the requested
        // length
        return len;
    }
    else {
        // Mode 1: API Copy Mode with Buffer List
        while (context->sendingBytes > MAXBUFFERSIZE) {
            if (context->isClosed)
                return -1;
            // Wait for send completion or timeout
            int timeout = context->connTimeout;
            auto ret = CxPlatEventWaitWithTimeout(context->sendEvent, context->connTimeout);
            if (!ret) {
                LLog::Log(LOG_INFO, "soc %p, send timeout  %d", context,
                    context->connTimeout);
                return -1;
            }
            LLog::Log(LOG_INFO, "sended notify");
        }
        // Acquire the lock to protect ring buffer access
        CxPlatLockAcquire(&context->sendBufferLock);

        // Copy the data to our buffer list
        context->sendListEnd = BufferListAppend(context->sendListEnd, buffer, len);
        if (context->sendList == NULL)
            context->sendList = context->sendListEnd;

        context->sending++;
        context->sendingBytes += len;


        // Check if we need to start a send operation with flow control
        BOOLEAN needToSend = !context->sendQuicBuffer[0].Buffer &&
                           context->bytesOutstanding < context->idealSendBuffer;

        // Release the lock
        CxPlatLockRelease(&context->sendBufferLock);

        // If no send is in progress and we have room, start one
        if (needToSend) {
            if (SendDataFromSocketBufferList(context) < 0) {
                return -1;
            }
        }

        // In this mode, we return immediately after copying to the buffer
        return len;
    }
}

// Write data using scatter/gather I/O
int QuicWritev(P2P_SOCKET soc, struct p2p_iovec* iov, int count) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL || context->connection == NULL || !context->isConnected ||
        count <= 0) {
        return -1;
    }

    // Create stream if not already created
    if (context->stream == NULL) {
        QUIC_STATUS status =
            MsQuic->StreamOpen(context->connection, QUIC_STREAM_OPEN_FLAG_NONE,
                StreamCallback, context, &context->stream);

        if (QUIC_FAILED(status)) {
            return -1;
        }

        status = MsQuic->StreamStart(context->stream, QUIC_STREAM_START_FLAG_NONE);

        if (QUIC_FAILED(status)) {
            MsQuic->StreamClose(context->stream);
            context->stream = NULL;
            return -1;
        }
    }

    // Calculate total length
    int totalLength = 0;
    for (int i = 0; i < count; i++) {
        totalLength += (int)iov[i].iov_len;
    }
    CxPlatEventReset(context->sendEvent);
    if (context->useDirectSendMode) {
        // Mode 2: Zero Copy Mode
        // In this mode, we rely on MsQuic's SendBufferingEnabled setting being
        // FALSE The application buffers are directly passed to MsQuic
        QUIC_SEND_FLAGS sendFlags = QUIC_SEND_FLAG_NONE;

        // Create buffers for sending
        QUIC_BUFFER* Buffers = (QUIC_BUFFER*)malloc(count * sizeof(QUIC_BUFFER));
        if (Buffers == NULL) {
            return -1;
        }

        for (int i = 0; i < count; i++) {
            Buffers[i].Buffer = (uint8_t*)iov[i].iov_base;
            Buffers[i].Length = (uint32_t)iov[i].iov_len;
        }

        CxPlatLockAcquire(&context->sendBufferLock);
        context->sending++;
        context->sendingBytes += totalLength;
        CxPlatLockRelease(&context->sendBufferLock);
        // Send data
        QUIC_STATUS status =
            MsQuic->StreamSend(context->stream, Buffers, count, sendFlags,
                Buffers);

        free(Buffers);

        if (QUIC_FAILED(status)) {
            return -1;
        }

        // Wait for send completion in blocking mode
        context->sendComplete = 0;
        context->sendCompleteBytes = 0;
        // Wait for send completion or timeout
        int timeout = context->connTimeout;
        auto ret = CxPlatEventWaitWithTimeout(context->sendEvent, context->connTimeout);
        if (!ret) {
            LLog::Log(LOG_INFO, "soc %p, send timeout  %d", context,
                context->connTimeout);
            return -1;
        }
        LLog::Log(LOG_INFO, "recv sended notify");

        // If send completed, return the number of bytes sent
        if (context->sending) {
            LLog::Log(LOG_ERROR, "sending error");
        }

        // Timeout but request was successfully submitted, return the requested
        // length
        return totalLength;
    }
    else {
        // Mode 1: API Copy Mode with Buffer List
        while (context->sendingBytes > MAXBUFFERSIZE) {
            if (context->isClosed)
                return -1;
            // Wait for send completion or timeout
            int timeout = context->connTimeout;
            auto ret = CxPlatEventWaitWithTimeout(context->sendEvent, context->connTimeout);
            if (!ret) {
                LLog::Log(LOG_INFO, "soc %p, send timeout  %d", context,
                    context->connTimeout);
                return -1;
            }
            LLog::Log(LOG_INFO, "sended notify");
        }

        // Acquire the lock to protect ring buffer access
        CxPlatLockAcquire(&context->sendBufferLock);

        // Copy the data to our buffer list
        context->sendListEnd = BufferListAppend(context->sendListEnd, iov, count);
        if (context->sendList == NULL)
            context->sendList = context->sendListEnd;

        context->sending++;
        context->sendingBytes += totalLength;

        // Check if we need to start a send operation with flow control
        BOOLEAN needToSend = !context->sendQuicBuffer[0].Buffer &&
                           context->bytesOutstanding < context->idealSendBuffer;

        // Release the lock
        CxPlatLockRelease(&context->sendBufferLock);

        // If no send is in progress and we have room, start one
        if (needToSend) {
            if (SendDataFromSocketBufferList(context) < 0) {
                return -1;
            }
        }

        // In this mode, we return immediately after copying to the buffer
        return totalLength;
    }
}

// Read data from the socket
int QuicRead(P2P_SOCKET soc, char* buffer, int len) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL || context->connection == NULL || !context->isConnected) {
        return -1;
    }
    LLog::Log(LOG_INFO, "soc:%x read %d", soc, len);
    if (context->useDirectReadMode) {
        // Direct read mode - read directly from queue
        CxPlatLockAcquire(&context->recvDirectBufferQueueLock);

        // Wait for data if queue is empty
        while (context->recvDirectBufferQueueHead == NULL) {
            CxPlatEventReset(context->recvEvent);
            CxPlatLockRelease(&context->recvDirectBufferQueueLock);

            // Wait for data to arrive
            LLog::Log(LOG_INFO, "soc %p, waiting for data in direct mode", context);

            auto ret = CxPlatEventWaitWithTimeout(context->recvEvent, context->connTimeout);
            if (!ret) {
                LLog::Log(LOG_INFO, "soc %p, read timeout %d", context, context->connTimeout);
                return 0;
            }
            LLog::Log(LOG_INFO, "direct mode: received data notification");

            CxPlatLockAcquire(&context->recvDirectBufferQueueLock);
        }

        // Now we have data in the queue, read from the head entry
        RECV_BUFFER_ENTRY* entry = context->recvDirectBufferQueueHead;
        int bytesRead = 0;

        // Determine how much data to copy
        uint64_t remainingLength = entry->totalLength - entry->consumedLength;
        int copyLen = (remainingLength > (uint64_t)len) ? len : (int)remainingLength;

        // Find the buffer and offset to start reading from
        uint64_t currentOffset = 0;
        uint32_t bufferIndex = 0;
        uint32_t bufferOffset = 0;

        // Skip to the current position
        while (bufferIndex < entry->bufferCount) {
            if (currentOffset + entry->buffers[bufferIndex].Length > entry->consumedLength) {
                // Found the buffer where our current position is
                bufferOffset = (uint32_t)(entry->consumedLength - currentOffset);
                break;
            }
            currentOffset += entry->buffers[bufferIndex].Length;
            bufferIndex++;
        }

        // Copy data from buffers
        while (bytesRead < copyLen && bufferIndex < entry->bufferCount) {
            uint32_t bytesToCopy = entry->buffers[bufferIndex].Length - bufferOffset;
            if (bytesToCopy > (copyLen - bytesRead)) {
                bytesToCopy = copyLen - bytesRead;
            }


            //printf("entry->buffers %p buffers = %p\n", entry->buffers, entry->buffers[bufferIndex].Buffer);
            memcpy(buffer + bytesRead,
                entry->buffers[bufferIndex].Buffer + bufferOffset,
                bytesToCopy);

            bytesRead += bytesToCopy;
            entry->consumedLength += bytesToCopy;

            // Move to next buffer if needed
            bufferIndex++;
            bufferOffset = 0;
        }

        // If we've consumed all data from this entry, complete and remove it
        if (entry->consumedLength >= entry->totalLength) {
            // Complete the receive operation
            MsQuic->StreamReceiveComplete(context->stream, entry->totalLength);

            // Remove the head entry from the queue
            context->recvDirectBufferQueueHead = entry->next;
            if (context->recvDirectBufferQueueHead == NULL) {
                context->recvDirectBufferQueueTail = NULL;
            }
            context->recvDirectBufferQueueSize--;

            // Free the buffer array and entry
            if (entry->buffers != NULL) {
                free(entry->buffers);
            }
            free(entry);

            LLog::Log(LOG_INFO, "soc %p, completed and removed buffer entry, queue size: %d\n",
                context, context->recvDirectBufferQueueSize);
        }

        CxPlatLockRelease(&context->recvDirectBufferQueueLock);
        return bytesRead;
    }
    else {
        // Copy mode - read from our internal buffer
        // If there's data in the receive buffer, return it
            // Wait for events
        CxPlatLockAcquire(&context->recvDirectBufferQueueLock);
        while (context->recvBuffer == NULL) {
            CxPlatLockRelease(&context->recvDirectBufferQueueLock);
            CxPlatEventReset(context->recvEvent);
            auto ret = CxPlatEventWaitWithTimeout(context->recvEvent, context->connTimeout);
            if (!ret) {
                LLog::Log(LOG_INFO, "soc %p, rec timeout  %d", context,
                    context->connTimeout);
                return 0;
            }
            LLog::Log(LOG_INFO, "recv notify");
            CxPlatLockAcquire(&context->recvDirectBufferQueueLock);
            if (!context->isConnected) {
                LLog::Log(LOG_INFO, "disconnect, break");
                break;
            }
        }

        int retLen = 0;
        while (context->recvBuffer && retLen < len) {
            int copyLen = context->recvBuffer->size - context->recvDataOffset;
            if (copyLen > len - retLen) {
                copyLen = len - retLen;
            }

            memcpy(buffer + retLen, context->recvBuffer->buffer + context->recvDataOffset, copyLen);
            context->recvDataOffset += copyLen;
            retLen += copyLen;

            // If all data has been read, reset the buffer
            if (context->recvDataOffset >= context->recvBuffer->size) {
                auto next = context->recvBuffer->list.Next;
                BufferListFree(context->recvBuffer);
                context->recvDataOffset = 0;
                context->recvBuffer = (BUFFER_LIST*)next;
                if (next == NULL)
                    context->recvBufferEnd = NULL;
            }

        }

        CxPlatLockRelease(&context->recvDirectBufferQueueLock);
        LLog::Log(LOG_INFO, "soc %p, buffering  %d", context,
            retLen);
        return retLen;
    }

    return 0; // No data available
}

// Accept a new connection
P2P_SOCKET QuicAccept(P2P_SOCKET soc, char* ipaddr, int ipaddr_len, int* port) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL || context->mode != MODE_SERVER ||
        context->listener == NULL) {
        return NULL;
    }
    ACCEPT_CONTEXT_LIST* list = context->contextList;

    // Check if connection queue is empty
    CxPlatLockAcquire(&context->acceptLock);
    while (list && list->context) {
        if (list->context->isAccepted)
            list = (ACCEPT_CONTEXT_LIST*)list->list.Next;
    }
    if (list == NULL) {
        CxPlatLockRelease(&context->acceptLock);
        // Wait for connection in blocking mode
        CxPlatEventReset(context->acceptEvent);
        CxPlatEventWaitForever(context->acceptEvent);
        CxPlatLockAcquire(&context->acceptLock);
    }

    list = context->contextList;
    while (list && list->context) {
        if (!list->context->isAccepted)
            break;
        list = (ACCEPT_CONTEXT_LIST*)list->list.Next;
    }

    if (list == NULL)
    {
        CxPlatLockRelease(&context->acceptLock);
        return NULL;
    }

    P2P_SOCKET_CONTEXT* newContext = list->context;
    CxPlatLockRelease(&context->acceptLock);

    return (P2P_SOCKET)newContext;
}

// Get the local port
int QuicGetLocalPort(P2P_SOCKET soc) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL) {
        return -1;
    }

    return context->localPort;
}

// Set the read mode (copy mode or direct mode)
int QuicSetReadMode(P2P_SOCKET soc, int directMode) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL) {
        return -1;
    }

    // If we're switching modes and have pending data, handle it appropriately
    if (context->useDirectReadMode != directMode) {
        if (context->useDirectReadMode) {
            // Switching from direct mode to copy mode
            // Clean up buffer queue
            CxPlatLockAcquire(&context->recvDirectBufferQueueLock);
            RECV_BUFFER_ENTRY* entry = context->recvDirectBufferQueueHead;
            while (entry != NULL) {
                RECV_BUFFER_ENTRY* next = entry->next;

                // Complete the receive operation for this entry
                if (context->stream != NULL) {
                    MsQuic->StreamReceiveComplete(context->stream, entry->totalLength);
                }

                // Free the buffer array
                if (entry->buffers != NULL) {
                    free(entry->buffers);
                }

                // Free the entry
                free(entry);
                entry = next;
            }
            context->recvDirectBufferQueueHead = NULL;
            context->recvDirectBufferQueueTail = NULL;
            context->recvDirectBufferQueueSize = 0;
            CxPlatLockRelease(&context->recvDirectBufferQueueLock);
        }

        // Update the mode
        context->useDirectReadMode = directMode;
    }

    return 0;
}
void QuicRelease(P2P_SOCKET soc) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL) {
        return;
    }

    LLog::Log(LOG_INFO, "QuicRelease %p", context);

    // Close listener
    if (context->isParent && context->listener != NULL) {
        CxPlatLockUninitialize(&context->acceptLock);
        CxPlatEventUninitialize(context->acceptEvent);

    }
    if (context->mode == MODE_CLIENT) {
        CxPlatEventUninitialize(context->connectEvent);
    }

    LLog::Log(LOG_INFO, "Clean up all streams");
    // Clean up all streams in the stream table
    CxPlatLockAcquire(&context->streamTableLock);

    // Use enumerator to clean up all streams
    CXPLAT_HASHTABLE_ENUMERATOR enumerator;
    CxPlatHashtableEnumerateBegin(&context->streamTable, &enumerator);

    CXPLAT_HASHTABLE_ENTRY* entry;
    while ((entry = CxPlatHashtableEnumerateNext(&context->streamTable, &enumerator)) != NULL) {
        P2P_STREAM_CONTEXT* streamContext = CXPLAT_CONTAINING_RECORD(entry, P2P_STREAM_CONTEXT, hashEntry);
        // Remove from hashtable first
        CxPlatHashtableRemove(&context->streamTable, entry, NULL);
        // Then destroy the context
        DestroyStreamContext(streamContext);
    }

    CxPlatHashtableEnumerateEnd(&context->streamTable, &enumerator);
    CxPlatHashtableUninitialize(&context->streamTable);
    context->activeStreams = 0;
    CxPlatLockRelease(&context->streamTableLock);

    // Clean up stream table lock
    CxPlatLockUninitialize(&context->streamTableLock);

    // Release default stream if it exists
    if (context->defaultStream != NULL) {
        StreamRelease(context->defaultStream);
        context->defaultStream = NULL;
    }
    LLog::Log(LOG_INFO, "delete recv buffer");
    // Clean up direct read mode resources if needed
    if (context->useDirectReadMode) {
        // Clean up buffer queue
        CxPlatLockAcquire(&context->recvDirectBufferQueueLock);
        RECV_BUFFER_ENTRY* entry = context->recvDirectBufferQueueHead;
        while (entry != NULL) {
            RECV_BUFFER_ENTRY* next = entry->next;

            // Complete the receive operation for this entry
            if (context->stream != NULL) {
                MsQuic->StreamReceiveComplete(context->stream, entry->totalLength);
            }

            // Free the buffer array
            if (entry->buffers != NULL) {
                free(entry->buffers);
            }

            // Free the entry
            free(entry);
            entry = next;
        }
        context->recvDirectBufferQueueHead = NULL;
        context->recvDirectBufferQueueTail = NULL;
        context->recvDirectBufferQueueSize = 0;

        while (context->recvBuffer)
        {
            BUFFER_LIST* next = (BUFFER_LIST*)context->recvBuffer->list.Next;
            BufferListFree(context->recvBuffer);
            context->recvBuffer = next;
        }
        CxPlatLockRelease(&context->recvDirectBufferQueueLock);

        // Uninitialize the lock
        CxPlatLockUninitialize(&context->recvDirectBufferQueueLock);
    }

    LLog::Log(LOG_INFO, "delete send buffer");
    // Clean up buffer queue
    CxPlatLockAcquire(&context->sendBufferLock);

    while (context->sendList)
    {
        BUFFER_LIST* next = (BUFFER_LIST*)context->sendList->list.Next;
        BufferListFree(context->sendList);
        context->sendList = next;
    }
    CxPlatLockRelease(&context->sendBufferLock);

    // Uninitialize the lock
    CxPlatLockUninitialize(&context->sendBufferLock);

    CxPlatEventUninitialize(context->recvEvent);

    CxPlatEventUninitialize(context->sendEvent);

    if (context->certPath != NULL) {
        free((void*)context->certPath);
    }

    if (context->privateKeyPath != NULL) {
        free((void*)context->privateKeyPath);
    }

    if (context->certData != NULL) {
        free((void*)context->certData);
    }

    if (context->privateKeyData != NULL) {
        free((void*)context->privateKeyData);
    }

    free(context);

    // Decrement the reference count for the shared resources
    UninitializeSharedResources();
    LLog::Log(LOG_INFO, "release successful");
}
// Set the send mode (API copy mode or send buffer mode)
int QuicSetSendMode(P2P_SOCKET soc, int sendBufferMode) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL) {
        return -1;
    }

    // If we're switching modes, update the mode
    if (context->useDirectSendMode != sendBufferMode) {
        context->useDirectSendMode = sendBufferMode;

        // If we have an active connection, update the SendBufferingEnabled setting
        if (context->connection != NULL) {
            QUIC_SETTINGS Settings = { 0 };
            Settings.IsSet.SendBufferingEnabled = TRUE;
            Settings.SendBufferingEnabled =
                sendBufferMode; // TRUE for API copy mode, FALSE for zero copy mode

            // Apply the settings to the connection
            QUIC_STATUS status =
                MsQuic->SetParam(context->connection, QUIC_PARAM_CONN_SETTINGS,
                    sizeof(Settings), &Settings);

            if (QUIC_FAILED(status)) {
                // Failed to update settings, but we'll continue with the mode change
                // anyway as our implementation will handle the difference
            }
        }
    }

    return 0;
}

// Poll for events
int QuicPoll(P2P_SOCKET soc, PollEvent* events, int timeout) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL || events == NULL) {
        return -1;
    }

    // Check for writable events
    if (events->events & P2PPOLLOUT) {
        if (context->isConnected) {
            events->revents |= P2PPOLLOUT;
            return 1;
        }
    }

    // Check for readable events
    if (events->events & P2PPOLLIN) {
        if (context->recvDataSize > 0) {
            events->revents |= P2PPOLLIN;
            return 1;
        }
    }

    // If timeout is 0, return immediately
    if (timeout == 0) {
        return 0;
    }

    // Wait for events
    CxPlatEventReset(context->recvEvent);
    auto ret = CxPlatEventWaitWithTimeout(context->recvEvent, timeout);
    if (ret)
        return 0;

    // Check for events again
    if ((events->events & P2PPOLLIN) && context->recvBuffer) {
        events->revents |= P2PPOLLIN;
        return 1;
    }

    return 0;
}

// Close the socket
int QuicClose(P2P_SOCKET soc) {
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    if (context == NULL) {
        return -1;
    }
    LLog::Log(LOG_INFO, "QuicClose %p", context);
    context->isClosed = 1;
    CxPlatEventSet(context->recvEvent);
    CxPlatEventSet(context->sendEvent);
    if(context->mode == MODE_CLIENT)
    CxPlatEventSet(context->connectEvent);

    if (context->isParent && context->listener != NULL) {
        CxPlatEventSet(context->acceptEvent);
    }



    // Close stream (for backward compatibility)
    if (context->stream != NULL) {
        LLog::Log(LOG_INFO, "quic stream shutdown %p", context->stream);
        MsQuic->StreamShutdown(context->stream, QUIC_STREAM_SHUTDOWN_FLAG_ABORT, 0);
        context->stream = NULL;
    }

    // Close connection
    if (context->connection != NULL) {
        LLog::Log(LOG_INFO, "quic connection shutdown %p", context->connection);
        MsQuic->ConnectionShutdown(context->connection, QUIC_CONNECTION_SHUTDOWN_FLAG_NONE, 0);
        context->connection = NULL;
    }

    // Close listener
    if (context->isParent && context->listener != NULL) {
        LLog::Log(LOG_INFO, "quic listen close %p", context->listener);
        MsQuic->ListenerClose(context->listener);
        context->listener = NULL;
    }

    if (context->isParent || context->mode == MODE_CLIENT) {
        // Close configuration
        if (context->configuration != NULL) {
            LLog::Log(LOG_INFO, "quic ConfigurationClose %p", context->configuration);
            MsQuic->ConfigurationClose(context->configuration);
            context->configuration = NULL;
        }
    }

    // Release the shared registration reference
    if (context->registration != NULL) {
        context->registration = NULL;
        // UninitializeSharedRegistration will be called at the end of this function
    }

    return 0;
}

// Callback handlers implementation
QUIC_STATUS
ClientConnectionCallback(HQUIC Connection, void* Context,
    QUIC_CONNECTION_EVENT* Event) {
    QUIC_STATUS Status;
    P2P_SOCKET_CONTEXT* socketContext = (P2P_SOCKET_CONTEXT*)Context;
    if (socketContext == NULL) {
        return QUIC_STATUS_INVALID_PARAMETER;
    }

    switch (Event->Type) {
    case QUIC_CONNECTION_EVENT_CONNECTED:

        // Store the worker thread ID for SendInline optimization
        socketContext->workerThreadID = CxPlatCurThreadID();
        socketContext->connection = Connection;
        if (QUIC_FAILED(Status = MsQuic->StreamOpen(Connection, QUIC_STREAM_OPEN_FLAG_NONE, StreamCallback, socketContext, &socketContext->stream))) {
            LLog::Log(LOG_INFO, "StreamOpen failed, 0x%x!", Status);
            break;
        }

        LLog::Log(LOG_INFO, " Starting conneciont %p", socketContext->connection);

        //
        // Starts the bidirectional stream. By default, the peer is not notified of
        // the stream being started until data is sent on the stream.
        //
        if (QUIC_FAILED(Status = MsQuic->StreamStart(socketContext->stream, QUIC_STREAM_START_FLAG_NONE))) {
            LLog::Log(LOG_ERROR, "StreamStart failed, 0x%x!", Status);
            MsQuic->StreamClose(socketContext->stream);
            socketContext->stream = NULL;
        }

        socketContext->isConnected = 1;
        CxPlatEventSet(socketContext->connectEvent);
        break;

    case QUIC_CONNECTION_EVENT_SHUTDOWN_INITIATED_BY_TRANSPORT:
    case QUIC_CONNECTION_EVENT_SHUTDOWN_INITIATED_BY_PEER:
        LLog::Log(LOG_INFO, "client connectionShutdown %p", Connection);
        break;
    case QUIC_CONNECTION_EVENT_SHUTDOWN_COMPLETE:
        LLog::Log(LOG_INFO, "client connectionClose %p", Connection);
        socketContext->isConnected = 0;
        MsQuic->ConnectionClose(Connection);
        break;

    case QUIC_CONNECTION_EVENT_PEER_STREAM_STARTED:
        // Handle peer-initiated stream
        socketContext->stream = Event->PEER_STREAM_STARTED.Stream;

        // Set the callback handler for the stream
        MsQuic->SetCallbackHandler(socketContext->stream, (void*)StreamCallback,
            socketContext);

        // Start receiving on the stream
        MsQuic->StreamReceiveSetEnabled(socketContext->stream, TRUE);
        LLog::Log(LOG_INFO, " Started stream %p", socketContext->stream);
        break;

    case QUIC_CONNECTION_EVENT_PEER_CERTIFICATE_RECEIVED:
        if (socketContext->certVerifyCallback != NULL) {
            // Extract certificate data from the event
            QUIC_CERTIFICATE* peerCert = Event->PEER_CERTIFICATE_RECEIVED.Certificate;
            QUIC_CERTIFICATE_CHAIN* peerChain = Event->PEER_CERTIFICATE_RECEIVED.Chain;

            LLog::Log(LOG_INFO, "PEER_CERTIFICATE_RECEIVED: Certificate=%p, Chain=%p, DeferredErrorFlags=0x%x, DeferredStatus=0x%x",
                      peerCert, peerChain,
                      Event->PEER_CERTIFICATE_RECEIVED.DeferredErrorFlags,
                      Event->PEER_CERTIFICATE_RECEIVED.DeferredStatus);

            char* certInfo = NULL;

            // Try to extract certificate information
            if (peerCert != NULL) {
                LLog::Log(LOG_INFO, "Attempting to extract certificate from Certificate field");
                certInfo = ExtractCertificateInfo(peerCert);
            } else if (peerChain != NULL) {
                LLog::Log(LOG_INFO, "Certificate field is NULL, attempting to extract from Chain field");
                // Try to extract from chain - the chain might contain the certificate
                certInfo = ExtractCertificateInfo((QUIC_CERTIFICATE*)peerChain);
            } else {
                LLog::Log(LOG_ERROR, "Both Certificate and Chain fields are NULL");
            }

            const char* certData = certInfo ? certInfo : "Certificate data (extraction failed)";
            LLog::Log(LOG_INFO, "Peer certificate received:\n%s\n", certData);

            // Call user-provided certificate verification callback
            bool result = socketContext->certVerifyCallback(socketContext, certData);

            // Free allocated buffer
            if (certInfo != NULL) {
                free(certInfo);
            }

            LLog::Log(LOG_INFO, "Certificate verification result: %s\n", result ? "SUCCESS" : "FAILED");
            return result ? QUIC_STATUS_SUCCESS : QUIC_STATUS_CERT_UNTRUSTED_ROOT;
        }
        break;
    }

    return QUIC_STATUS_SUCCESS;
}

static void ShutdownServerConnection(P2P_SOCKET_CONTEXT* socketContext, HQUIC Connection) {
    CxPlatLockAcquire(&socketContext->acceptLock);
    auto clist = socketContext->contextList;
    while (clist) {
        if (clist->context && clist->context->connection == Connection) {
            clist->context->connection = NULL;
            clist->context->isConnected = 0;
            CxPlatEventSet(clist->context->recvEvent);
            CxPlatEventSet(clist->context->sendEvent);
            LLog::Log(LOG_INFO, "server connection shutdown %p %p", Connection, clist->context);
            break;
        }
        clist = (ACCEPT_CONTEXT_LIST*)clist->list.Next;
    }

    CxPlatLockRelease(&socketContext->acceptLock);
    LLog::Log(LOG_INFO, "exit server connection shutdown %p", Connection);
}

QUIC_STATUS
ServerConnectionCallback(HQUIC Connection, void* Context,
    QUIC_CONNECTION_EVENT* Event) {
    P2P_SOCKET_CONTEXT* socketContext = (P2P_SOCKET_CONTEXT*)Context;
    if (socketContext == NULL) {
        return QUIC_STATUS_INVALID_PARAMETER;
    }

    switch (Event->Type) {
    case QUIC_CONNECTION_EVENT_CONNECTED:
    {
        P2P_SOCKET_CONTEXT* c = (P2P_SOCKET_CONTEXT*)P2pMalloc(sizeof(P2P_SOCKET_CONTEXT));
        if (c == NULL) {
            return QUIC_STATUS_OUT_OF_MEMORY;
        }

        c->connection = Connection;
        c->mode = MODE_SERVER;
        c->type = socketContext->type;
        c->isParent = 0;

        // Get a new reference to the shared resources
        c->registration = InitializeSharedResources();

        c->configuration = socketContext->configuration; // Shared configuration
        c->isConnected = 1; // Connection established
        c->connTimeout = socketContext->connTimeout;

        // Copy local address information
        memcpy(c->localIp, socketContext->localIp, MAXIPLEN);
        c->localPort = socketContext->localPort;
        c->useDirectReadMode = socketContext->useDirectReadMode; // Inherit from listener
        c->remotePort = socketContext->remotePort;
        memcpy(c->remoteIp, socketContext->remoteIp, MAXIPLEN);
        c->parent = socketContext;

        // Initialize flow control variables for single stream
        c->bytesOutstanding = 0;
        c->idealSendBuffer = 2 * 1024 * 1024; // Default 64KB, will be updated by QUIC
        c->bytesSent = 0;
        c->bytesAcked = 0;

                // Initialize stream management
        CXPLAT_HASHTABLE* streamTablePtr = &c->streamTable;
        CxPlatHashtableInitialize(&streamTablePtr, CXPLAT_HASH_MIN_SIZE);
        CxPlatLockInitialize(&c->streamTableLock);
        // Initialize the lock for thread safety
        CxPlatLockInitialize(&c->sendBufferLock);
        CxPlatEventInitialize(&c->sendEvent, TRUE, FALSE);

        CxPlatLockInitialize(&c->recvDirectBufferQueueLock);
        CxPlatEventInitialize(&c->recvEvent, TRUE, FALSE);
        ACCEPT_CONTEXT_LIST* list = (ACCEPT_CONTEXT_LIST*)P2pMalloc(sizeof(ACCEPT_CONTEXT_LIST));
        if (list == NULL) {
            return QUIC_STATUS_OUT_OF_MEMORY;
        }
        list->context = c;
        c->isConnected = 1;
        c->isAccepted = 0;
        // Store the worker thread ID for SendInline optimization
        c->workerThreadID = CxPlatCurThreadID();
        CxPlatLockAcquire(&socketContext->acceptLock);
        if (socketContext->contextList == NULL)
            socketContext->contextList = list;
        else {
            auto clist = socketContext->contextList;
            while (clist && clist->list.Next) {
                clist = (ACCEPT_CONTEXT_LIST*)clist->list.Next;
            }
            clist->list.Next = (CXPLAT_SLIST_ENTRY*)list;
        }
        CxPlatLockRelease(&socketContext->acceptLock);

        CxPlatEventSet(socketContext->acceptEvent);

        LLog::Log(LOG_INFO, "new connection coming %p %p", Connection, c);
        break;
    }
    case QUIC_CONNECTION_EVENT_SHUTDOWN_INITIATED_BY_TRANSPORT:
    case QUIC_CONNECTION_EVENT_SHUTDOWN_INITIATED_BY_PEER:
        LLog::Log(LOG_INFO, "server connectionShutdown %p", Connection);
        ShutdownServerConnection(socketContext, Connection);
        break;
    case QUIC_CONNECTION_EVENT_SHUTDOWN_COMPLETE:
    {
        LLog::Log(LOG_INFO, "server connectionClose %p", Connection);
        MsQuic->ConnectionClose(Connection);
        ShutdownServerConnection(socketContext, Connection);
        break;
    }

    case QUIC_CONNECTION_EVENT_PEER_STREAM_STARTED:
    {
        P2P_SOCKET_CONTEXT* c = NULL;
        CxPlatLockAcquire(&socketContext->acceptLock);
        auto clist = socketContext->contextList;
        while (clist) {
            if (clist->context && clist->context->connection == Connection) {
                c = clist->context;
                LLog::Log(LOG_INFO, "connection stream start %p %p", Connection, socketContext);
                break;
            }
            clist = (ACCEPT_CONTEXT_LIST*)clist->list.Next;
        }

        CxPlatLockRelease(&socketContext->acceptLock);
        // Handle peer-initiated stream
        if (c) {
            c->stream = Event->PEER_STREAM_STARTED.Stream;

            // Set the callback handler for the stream
            MsQuic->SetCallbackHandler(c->stream, (void*)StreamCallback,
                c);

            // Start receiving on the stream
            MsQuic->StreamReceiveSetEnabled(c->stream, TRUE);
            LLog::Log(LOG_INFO, "new stream coming %p %p", Connection, c->stream);
        }
        else
        {
            LLog::Log(LOG_INFO, "new stream coming, not found %p %p", socketContext, Connection);
        }

        break;
    }

    case QUIC_CONNECTION_EVENT_PEER_CERTIFICATE_RECEIVED:
        if (socketContext->certVerifyCallback != NULL) {
            // Extract certificate data from the event
            QUIC_CERTIFICATE* peerCert = Event->PEER_CERTIFICATE_RECEIVED.Certificate;
            QUIC_CERTIFICATE_CHAIN* peerChain = Event->PEER_CERTIFICATE_RECEIVED.Chain;

            LLog::Log(LOG_INFO, "SERVER PEER_CERTIFICATE_RECEIVED: Certificate=%p, Chain=%p, DeferredErrorFlags=0x%x, DeferredStatus=0x%x",
                      peerCert, peerChain,
                      Event->PEER_CERTIFICATE_RECEIVED.DeferredErrorFlags,
                      Event->PEER_CERTIFICATE_RECEIVED.DeferredStatus);

            char* certInfo = NULL;

            // Try to extract certificate information
            if (peerCert != NULL) {
                LLog::Log(LOG_INFO, "SERVER: Attempting to extract certificate from Certificate field");
                certInfo = ExtractCertificateInfo(peerCert);
            } else if (peerChain != NULL) {
                LLog::Log(LOG_INFO, "SERVER: Certificate field is NULL, attempting to extract from Chain field");
                // Try to extract from chain - the chain might contain the certificate
                certInfo = ExtractCertificateInfo((QUIC_CERTIFICATE*)peerChain);
            } else {
                LLog::Log(LOG_ERROR, "SERVER: Both Certificate and Chain fields are NULL");
            }

            const char* certData = certInfo ? certInfo : "Certificate data (extraction failed)";
            LLog::Log(LOG_INFO, "SERVER: Peer certificate received:\n%s\n", certData);

            // Call user-provided certificate verification callback
            bool result = socketContext->certVerifyCallback(socketContext, certData);

            // Free allocated buffer
            if (certInfo != NULL) {
                free(certInfo);
            }

            LLog::Log(LOG_INFO, "SERVER: cert %x %x, verification result: %s\n", Connection, result, result ? "SUCCESS" : "FAILED");

            return result ? QUIC_STATUS_SUCCESS : QUIC_STATUS_CERT_UNTRUSTED_ROOT;
        }
        break;
    }

    return QUIC_STATUS_SUCCESS;
}

QUIC_STATUS
StreamCallback(HQUIC Stream, void* Context, QUIC_STREAM_EVENT* Event) {
    P2P_SOCKET_CONTEXT* socketContext = (P2P_SOCKET_CONTEXT*)Context;

    if (socketContext == NULL) {
        return QUIC_STATUS_INVALID_PARAMETER;
    }

    switch (Event->Type) {
    case QUIC_STREAM_EVENT_RECEIVE:
        // Process received data
        if (Event->RECEIVE.BufferCount > 0 && Event->RECEIVE.Buffers != NULL) {
            // Calculate total data length
            uint64_t totalLength = 0;
            for (uint32_t i = 0; i < Event->RECEIVE.BufferCount; i++) {
                totalLength += Event->RECEIVE.Buffers[i].Length;
            }
            LLog::Log(LOG_INFO, "soc:%x, reciving  %d, direct mode: %d isclient: %d", socketContext,
                totalLength, socketContext->useDirectReadMode, socketContext->mode);
            if (socketContext->useDirectReadMode) {
                // Direct read mode - store buffer references in the queue
                // Create a new buffer entry
                RECV_BUFFER_ENTRY* newEntry =
                    (RECV_BUFFER_ENTRY*)P2pMalloc(sizeof(RECV_BUFFER_ENTRY));
                if (newEntry == NULL) {
                    return QUIC_STATUS_OUT_OF_MEMORY;
                }

                // Initialize the new entry
                // Allocate memory for buffer array (just the QUIC_BUFFER structures, not the data)
                newEntry->buffers = (QUIC_BUFFER*)P2pMalloc(Event->RECEIVE.BufferCount * sizeof(QUIC_BUFFER));
                if (newEntry->buffers == NULL) {
                    free(newEntry);
                    return QUIC_STATUS_OUT_OF_MEMORY;
                }

                // Copy buffer information - this copies the QUIC_BUFFER structures but not the data
                for (uint32_t i = 0; i < Event->RECEIVE.BufferCount; i++) {
                    newEntry->buffers[i] = Event->RECEIVE.Buffers[i];  // This copies the Length and Buffer pointer
                }

                newEntry->bufferCount = Event->RECEIVE.BufferCount;
                newEntry->totalLength = totalLength;
                newEntry->consumedLength = 0;
                newEntry->next = NULL;

                // Add the entry to the queue
                CxPlatLockAcquire(&socketContext->recvDirectBufferQueueLock);

                if (socketContext->recvDirectBufferQueueTail == NULL) {
                    // Queue is empty, set as head and tail
                    socketContext->recvDirectBufferQueueHead = newEntry;
                    socketContext->recvDirectBufferQueueTail = newEntry;
                }
                else {
                    // Add to the end of the queue
                    socketContext->recvDirectBufferQueueTail->next = newEntry;
                    socketContext->recvDirectBufferQueueTail = newEntry;
                }
                socketContext->recvDirectBufferQueueSize++;

                // Signal that data is available
                CxPlatEventSet(socketContext->recvEvent);
                //printf("newEntry->buffers %p buffers = %p\n", newEntry->buffers, newEntry->buffers[0].Buffer);
                LLog::Log(LOG_INFO, "direct mode: signaling data available, queue size: %d\n", socketContext->recvDirectBufferQueueSize);
                CxPlatLockRelease(&socketContext->recvDirectBufferQueueLock);

                // Return QUIC_STATUS_PENDING to indicate we'll process the data later
                // This tells MsQuic not to release the buffers yet
                return QUIC_STATUS_PENDING;
            }
            else {
                // Copy mode - copy data to our buffer
                // Ensure receive buffer is large enough

                // Copy data to receive buffer
                CxPlatLockAcquire(&socketContext->recvDirectBufferQueueLock);


                uint64_t offset = 0;
                for (uint32_t i = 0; i < Event->RECEIVE.BufferCount; i++) {
                    BUFFER_LIST* bl = BufferListAppend(socketContext->recvBufferEnd, (const char*)Event->RECEIVE.Buffers[i].Buffer,
                        Event->RECEIVE.Buffers[i].Length);
                    socketContext->recvBufferEnd = bl;
                    if (socketContext->recvBuffer == NULL)
                        socketContext->recvBuffer = bl;
                }
                CxPlatLockRelease(&socketContext->recvDirectBufferQueueLock);

                socketContext->dataAvailable = 1;
                CxPlatEventSet(socketContext->recvEvent);
                LLog::Log(LOG_INFO, "notify");
            }
        }
        break;

    case QUIC_STREAM_EVENT_SEND_COMPLETE: {
        // Get the length of data that was sent
        uint32_t sentLength = 0;
        int sentBufferCount = 0;
        if (Event->SEND_COMPLETE.ClientContext != NULL) {
            // In our case, ClientContext points to the QUIC_BUFFER array
            QUIC_BUFFER* buffers = (QUIC_BUFFER*)Event->SEND_COMPLETE.ClientContext;
            if (buffers == socketContext->sendQuicBuffer) {
                // Calculate total sent length from the buffers
                // Count until we find a NULL buffer or reach the array limit
                for (int i = 0; i < MAX_SEND_BUFFER_NUM && buffers[i].Buffer != NULL; i++) {
                    sentLength += buffers[i].Length;
                    sentBufferCount++;
                }
            }
        }

        BOOLEAN hasMoreData = FALSE;

        // Mark send as complete for blocking mode
        socketContext->sendComplete = 1;

        // If we're in API copy mode, consume the data from the ring buffer
        if (socketContext->useDirectSendMode)
        {
            CxPlatLockAcquire(&socketContext->sendBufferLock);
            socketContext->sending--;
            CxPlatLockRelease(&socketContext->sendBufferLock);
            CxPlatEventSet(socketContext->sendEvent);
            LLog::Log(LOG_ERROR, "notify send callback %d", socketContext->sending);
        } else {
            // Handle flow control for non-useDirectSendMode (single stream)
            // Acquire the lock to protect ring buffer access
            CxPlatLockAcquire(&socketContext->sendBufferLock);

            if (Event->SEND_COMPLETE.ClientContext != socketContext->sendQuicBuffer) {
                LLog::Log(LOG_ERROR, "error send callback");
            }

            // Update flow control counters
            //printf("QUIC_STREAM_EVENT_SEND_COMPLETE sentLength %d\n", sentLength);
            if (sentLength > 0) {
                socketContext->bytesOutstanding -= sentLength;
                socketContext->bytesAcked += sentLength;
            }

            // Remove sent buffers from the send list based on actual sent count
            for (int i = 0; i < sentBufferCount && socketContext->sendList != NULL; i++) {
                auto list = socketContext->sendList;
                socketContext->sendList = (BUFFER_LIST*)socketContext->sendList->list.Next;
                BufferListFree(list);
                socketContext->sending--;
                socketContext->sendingBytes -= socketContext->sendQuicBuffer[i].Length;
                socketContext->sendQuicBuffer[i].Buffer = NULL;
                socketContext->sendQuicBuffer[i].Length = 0;
            }
            if (socketContext->sendList == NULL)
                socketContext->sendListEnd = NULL;

            hasMoreData = !!socketContext->sendList;

            // Release the lock
            CxPlatLockRelease(&socketContext->sendBufferLock);
            CxPlatEventSet(socketContext->sendEvent);

            // If there's more data and we have room in the ideal buffer, send it
            if (hasMoreData && socketContext->bytesOutstanding < socketContext->idealSendBuffer) {
                SendDataFromSocketBufferList(socketContext);
            }
        }
        //printf("socket:%p, send complete, bytes: %u\n", socketContext, sentLength);
        LLog::Log(LOG_INFO, "socket:%p, send complete, bytes: %u\n", socketContext, sentLength);
    } break;
    case QUIC_STREAM_EVENT_PEER_SEND_ABORTED:
        //
        // The peer gracefully shut down its send direction of the stream.
        //
        LLog::Log(LOG_INFO, "[strm][%p] Peer aborted", Stream);
        break;
    case QUIC_STREAM_EVENT_PEER_SEND_SHUTDOWN:
        //
        // The peer aborted its send direction of the stream.
        //
        LLog::Log(LOG_INFO, "[strm][%p] Peer shut down", Stream);
        break;
    case QUIC_STREAM_EVENT_SHUTDOWN_COMPLETE:
        //
        // Both directions of the stream have been shut down and MsQuic is done
        // with the stream. It can now be safely cleaned up.
        //
        LLog::Log(LOG_INFO, "[strm][%p] All done", Stream);
        if (!Event->SHUTDOWN_COMPLETE.AppCloseInProgress) {
            MsQuic->StreamClose(Stream);
        }
        break;

    case QUIC_STREAM_EVENT_IDEAL_SEND_BUFFER_SIZE:
        // Update ideal send buffer size and try to send more data if available (single stream)
        if (!socketContext->useDirectSendMode &&
            socketContext->idealSendBuffer != Event->IDEAL_SEND_BUFFER_SIZE.ByteCount) {

            socketContext->idealSendBuffer = Event->IDEAL_SEND_BUFFER_SIZE.ByteCount;
           // printf("socket:%p, ideal send buffer size updated to %llu\n",
           //     socketContext, socketContext->idealSendBuffer);
            LLog::Log(LOG_INFO, "socket:%p, ideal send buffer size updated to %llu\n",
                     socketContext, socketContext->idealSendBuffer);

            // If we have pending data and room in the buffer, try to send more
            // if (socketContext->sendList != NULL &&
            //     socketContext->bytesOutstanding < socketContext->idealSendBuffer) {
            //     SendDataFromSocketBufferList(socketContext);
            // }
        }
        break;
    }

    return QUIC_STATUS_SUCCESS;
}

QUIC_STATUS
ListenerCallback(HQUIC Listener, void* Context, QUIC_LISTENER_EVENT* Event) {
    P2P_SOCKET_CONTEXT* socketContext = (P2P_SOCKET_CONTEXT*)Context;
    if (socketContext == NULL) {
        return QUIC_STATUS_INVALID_PARAMETER;
    }

    switch (Event->Type) {
    case QUIC_LISTENER_EVENT_NEW_CONNECTION: {
        // Get remote address information
        const QUIC_ADDR* remoteAddr = Event->NEW_CONNECTION.Info->RemoteAddress;
        char ipStr[INET6_ADDRSTRLEN] = { 0 };
        uint16_t port = 0;

#ifdef _WIN32
        // Windows platform
        struct sockaddr* saddr = (struct sockaddr*)remoteAddr;
        if (saddr->sa_family == AF_INET) {
            struct sockaddr_in* addr4 = (struct sockaddr_in*)remoteAddr;
            inet_ntop(AF_INET, &addr4->sin_addr, ipStr, sizeof(ipStr));
            port = ntohs(addr4->sin_port);
        }
        else if (saddr->sa_family == AF_INET6) {
            struct sockaddr_in6* addr6 = (struct sockaddr_in6*)remoteAddr;
            inet_ntop(AF_INET6, &addr6->sin6_addr, ipStr, sizeof(ipStr));
            port = ntohs(addr6->sin6_port);
        }
#else
        // Non-Windows platforms
        if (remoteAddr->Ip.sa_family == AF_INET) {
            struct sockaddr_in* addr4 = (struct sockaddr_in*)remoteAddr;
            inet_ntop(AF_INET, &addr4->sin_addr, ipStr, sizeof(ipStr));
            port = ntohs(addr4->sin_port);
        }
        else if (remoteAddr->Ip.sa_family == AF_INET6) {
            struct sockaddr_in6* addr6 = (struct sockaddr_in6*)remoteAddr;
            inet_ntop(AF_INET6, &addr6->sin6_addr, ipStr, sizeof(ipStr));
            port = ntohs(addr6->sin6_port);
        }
#endif
        memcpy(socketContext->remoteIp, ipStr, MAXIPLEN);
        socketContext->remotePort = port;

        MsQuic->SetCallbackHandler(Event->NEW_CONNECTION.Connection,
            (void*)ServerConnectionCallback, socketContext);

        QUIC_STATUS status = MsQuic->ConnectionSetConfiguration(
            Event->NEW_CONNECTION.Connection, socketContext->configuration);
        LLog::Log(LOG_INFO, "listen incoming %p %s %d", Event->NEW_CONNECTION.Connection, ipStr, port);

        if (QUIC_FAILED(status)) {
            return status;
        }

        return QUIC_STATUS_SUCCESS;
    } break;
    }

    return QUIC_STATUS_SUCCESS;
}

// Stream management helper functions implementation

// Create a new stream context
static P2P_STREAM_CONTEXT* CreateStreamContext(P2P_SOCKET_CONTEXT* socketContext, struct StreamOptions* options) {
    if (socketContext == NULL) {
        return NULL;
    }

    P2P_STREAM_CONTEXT* streamContext = (P2P_STREAM_CONTEXT*)P2pMalloc(sizeof(P2P_STREAM_CONTEXT));
    if (streamContext == NULL) {
        return NULL;
    }

    // Initialize basic fields
    streamContext->socket = socketContext;
    streamContext->state = STREAM_STATE_IDLE;
    streamContext->refCount = 1;
    streamContext->quicStream = NULL;

    // Copy stream options
    if (options) {
        streamContext->options = *options;
    } else {
        // Default options
        streamContext->options.unidirectional = 0;
        streamContext->options.priority = 0;
        streamContext->options.buffer_size = 64 * 1024; // 64KB default
    }

    // Initialize locks and events
    CxPlatLockInitialize(&streamContext->bufferLock);
    CxPlatLockInitialize(&streamContext->sendBufferLock);
    CxPlatLockInitialize(&streamContext->eventQueueLock);
    CxPlatLockInitialize(&streamContext->recvDirectBufferQueueLock);
    CxPlatLockInitialize(&streamContext->refLock);

    CxPlatEventInitialize(&streamContext->eventAvailable, FALSE, FALSE);
    CxPlatEventInitialize(&streamContext->recvEvent, FALSE, FALSE);
    CxPlatEventInitialize(&streamContext->sendEvent, FALSE, FALSE);

    // Initialize event queue
    CxPlatListInitializeHead(&streamContext->eventQueue);

    // Initialize buffer fields
    streamContext->recvBuffer = NULL;
    streamContext->recvBufferEnd = NULL;
    streamContext->sendList = NULL;
    streamContext->sendListEnd = NULL;
    streamContext->useDirectReadMode = 0;
    streamContext->useDirectSendMode = 0;

    // Initialize direct buffer queue
    streamContext->recvDirectBufferQueueHead = NULL;
    streamContext->recvDirectBufferQueueTail = NULL;
    streamContext->recvDirectBufferQueueSize = 0;

    // Initialize statistics
    streamContext->dataAvailable = 0;
    streamContext->sendComplete = 0;
    streamContext->sendCompleteBytes = 0;
    streamContext->sending = 0;
    streamContext->sendingBytes = 0;
    streamContext->lastError = 0;

    // Initialize callback fields
    streamContext->callback = NULL;
    streamContext->userData = NULL;

    return streamContext;
}

// Destroy a stream context
static void DestroyStreamContext(P2P_STREAM_CONTEXT* streamContext) {
    if (streamContext == NULL) {
        return;
    }

    // Close QUIC stream if still open
    if (streamContext->quicStream != NULL) {
        MsQuic->StreamShutdown(streamContext->quicStream, QUIC_STREAM_SHUTDOWN_FLAG_ABORT, 0);
        streamContext->quicStream = NULL;
    }

    // Clean up receive buffers
    while (streamContext->recvBuffer != NULL) {
        BUFFER_LIST* next = (BUFFER_LIST*)streamContext->recvBuffer->list.Next;
        BufferListFree(streamContext->recvBuffer);
        streamContext->recvBuffer = next;
    }

    // Clean up send buffers
    while (streamContext->sendList != NULL) {
        BUFFER_LIST* next = (BUFFER_LIST*)streamContext->sendList->list.Next;
        BufferListFree(streamContext->sendList);
        streamContext->sendList = next;
    }

    // Clean up direct receive buffer queue
    CxPlatLockAcquire(&streamContext->recvDirectBufferQueueLock);
    while (streamContext->recvDirectBufferQueueHead != NULL) {
        RECV_BUFFER_ENTRY* next = streamContext->recvDirectBufferQueueHead->next;
        free(streamContext->recvDirectBufferQueueHead);
        streamContext->recvDirectBufferQueueHead = next;
    }
    CxPlatLockRelease(&streamContext->recvDirectBufferQueueLock);

    // Clean up event queue
    CxPlatLockAcquire(&streamContext->eventQueueLock);
    while (!CxPlatListIsEmpty(&streamContext->eventQueue)) {
        CXPLAT_LIST_ENTRY* entry = CxPlatListRemoveHead(&streamContext->eventQueue);
        STREAM_EVENT_ENTRY* eventEntry = CXPLAT_CONTAINING_RECORD(entry, STREAM_EVENT_ENTRY, list);
        free(eventEntry);
    }
    CxPlatLockRelease(&streamContext->eventQueueLock);

    // Uninitialize locks and events
    CxPlatLockUninitialize(&streamContext->bufferLock);
    CxPlatLockUninitialize(&streamContext->sendBufferLock);
    CxPlatLockUninitialize(&streamContext->eventQueueLock);
    CxPlatLockUninitialize(&streamContext->recvDirectBufferQueueLock);
    CxPlatLockUninitialize(&streamContext->refLock);

    CxPlatEventUninitialize(streamContext->eventAvailable);
    CxPlatEventUninitialize(streamContext->recvEvent);
    CxPlatEventUninitialize(streamContext->sendEvent);

    // Free the context
    free(streamContext);
}

// Add stream to socket's stream table
static void AddStreamToSocket(P2P_SOCKET_CONTEXT* socketContext, P2P_STREAM_CONTEXT* streamContext) {
    if (socketContext == NULL || streamContext == NULL) {
        return;
    }

    CxPlatLockAcquire(&socketContext->streamTableLock);

    // Assign stream ID
    streamContext->streamId = ++socketContext->nextStreamId;

    // Initialize hash entry
    streamContext->hashEntry.Signature = streamContext->streamId;

    // Add to hash table using proper API
    CxPlatHashtableInsert(&socketContext->streamTable, &streamContext->hashEntry, streamContext->streamId, NULL);

    // Update counters
    socketContext->activeStreams++;

    CxPlatLockRelease(&socketContext->streamTableLock);
}

// Remove stream from socket's stream table
static void RemoveStreamFromSocket(P2P_SOCKET_CONTEXT* socketContext, P2P_STREAM_CONTEXT* streamContext) {
    if (socketContext == NULL || streamContext == NULL) {
        return;
    }

    CxPlatLockAcquire(&socketContext->streamTableLock);

    // Remove from hash table using proper API
    CxPlatHashtableRemove(&socketContext->streamTable, &streamContext->hashEntry, NULL);

    // Update counters
    if (socketContext->activeStreams > 0) {
        socketContext->activeStreams--;
    }

    CxPlatLockRelease(&socketContext->streamTableLock);
}

// Find stream by ID
static P2P_STREAM_CONTEXT* FindStreamById(P2P_SOCKET_CONTEXT* socketContext, uint64_t streamId) {
    if (socketContext == NULL) {
        return NULL;
    }

    CxPlatLockAcquire(&socketContext->streamTableLock);

    // Use proper hashtable lookup API
    CXPLAT_HASHTABLE_ENTRY* entry = CxPlatHashtableLookup(&socketContext->streamTable, streamId, NULL);
    P2P_STREAM_CONTEXT* streamContext = NULL;

    if (entry != NULL) {
        streamContext = CXPLAT_CONTAINING_RECORD(entry, P2P_STREAM_CONTEXT, hashEntry);
        StreamAddRef(streamContext); // Add reference before returning
    }

    CxPlatLockRelease(&socketContext->streamTableLock);

    return streamContext;
}

// Add reference to stream
static void StreamAddRef(P2P_STREAM_CONTEXT* streamContext) {
    if (streamContext == NULL) {
        return;
    }

    CxPlatLockAcquire(&streamContext->refLock);
    streamContext->refCount++;
    CxPlatLockRelease(&streamContext->refLock);
}

// Release reference to stream
static void StreamRelease(P2P_STREAM_CONTEXT* streamContext) {
    if (streamContext == NULL) {
        return;
    }

    CxPlatLockAcquire(&streamContext->refLock);
    streamContext->refCount--;
    int refCount = streamContext->refCount;
    CxPlatLockRelease(&streamContext->refLock);

    if (refCount == 0) {
        DestroyStreamContext(streamContext);
    }
}

// Queue a stream event
static void QueueStreamEvent(P2P_STREAM_CONTEXT* streamContext, struct StreamEvent* event) {
    if (streamContext == NULL || event == NULL) {
        return;
    }

    STREAM_EVENT_ENTRY* eventEntry = (STREAM_EVENT_ENTRY*)malloc(sizeof(STREAM_EVENT_ENTRY));
    if (eventEntry == NULL) {
        return;
    }

    eventEntry->event = *event;

    CxPlatLockAcquire(&streamContext->eventQueueLock);
    CxPlatListInsertTail(&streamContext->eventQueue, &eventEntry->list);
    CxPlatLockRelease(&streamContext->eventQueueLock);

    // Signal that an event is available
    CxPlatEventSet(streamContext->eventAvailable);

    // If callback is set, call it immediately
    if (streamContext->callback != NULL) {
        streamContext->callback((P2P_STREAM)streamContext, event, streamContext->userData);
    }
}

// Dequeue a stream event (for poll mode)
static int DequeueStreamEvent(P2P_STREAM_CONTEXT* streamContext, struct StreamEvent* event) {
    if (streamContext == NULL || event == NULL) {
        return 0;
    }

    CxPlatLockAcquire(&streamContext->eventQueueLock);

    if (CxPlatListIsEmpty(&streamContext->eventQueue)) {
        CxPlatLockRelease(&streamContext->eventQueueLock);
        return 0;
    }

    CXPLAT_LIST_ENTRY* entry = CxPlatListRemoveHead(&streamContext->eventQueue);
    STREAM_EVENT_ENTRY* eventEntry = CXPLAT_CONTAINING_RECORD(entry, STREAM_EVENT_ENTRY, list);

    *event = eventEntry->event;
    free(eventEntry);

    // Reset event if queue is empty
    if (CxPlatListIsEmpty(&streamContext->eventQueue)) {
        CxPlatEventReset(streamContext->eventAvailable);
    }

    CxPlatLockRelease(&streamContext->eventQueueLock);

    return 1;
}

// Internal Stream API implementations

// Create a new stream (internal implementation)
static P2P_STREAM P2pStreamCreateInternal(P2P_SOCKET soc, struct StreamOptions* options) {
    P2P_SOCKET_CONTEXT* socketContext = (P2P_SOCKET_CONTEXT*)soc;
    if (socketContext == NULL || !socketContext->isConnected) {
        return NULL;
    }

    // Check if we've reached the maximum number of streams
    CxPlatLockAcquire(&socketContext->streamTableLock);
    if (socketContext->activeStreams >= socketContext->maxStreams) {
        CxPlatLockRelease(&socketContext->streamTableLock);
        return NULL;
    }
    CxPlatLockRelease(&socketContext->streamTableLock);

    // Create stream context
    P2P_STREAM_CONTEXT* streamContext = CreateStreamContext(socketContext, options);
    if (streamContext == NULL) {
        return NULL;
    }

    // Create QUIC stream
    QUIC_STREAM_OPEN_FLAGS flags = QUIC_STREAM_OPEN_FLAG_NONE;
    if (options && options->unidirectional) {
        flags |= QUIC_STREAM_OPEN_FLAG_UNIDIRECTIONAL;
    }

    QUIC_STATUS status = MsQuic->StreamOpen(
        socketContext->connection,
        flags,
        NewStreamCallback,
        streamContext,
        &streamContext->quicStream
    );

    if (QUIC_FAILED(status)) {
        DestroyStreamContext(streamContext);
        return NULL;
    }

    // Start the stream
    status = MsQuic->StreamStart(streamContext->quicStream, QUIC_STREAM_START_FLAG_NONE);
    if (QUIC_FAILED(status)) {
        MsQuic->StreamClose(streamContext->quicStream);
        streamContext->quicStream = NULL;
        DestroyStreamContext(streamContext);
        return NULL;
    }

    // Add stream to socket's stream table
    AddStreamToSocket(socketContext, streamContext);

    // Update stream state
    streamContext->state = STREAM_STATE_OPEN;

    return (P2P_STREAM)streamContext;
}

// Close a stream (internal implementation)
static int P2pStreamCloseInternal(P2P_STREAM stream) {
    P2P_STREAM_CONTEXT* streamContext = (P2P_STREAM_CONTEXT*)stream;
    if (streamContext == NULL) {
        return -1;
    }

    // Update state
    streamContext->state = STREAM_STATE_CLOSED;

    // Remove from socket's stream table
    RemoveStreamFromSocket(streamContext->socket, streamContext);

    // Close QUIC stream
    if (streamContext->quicStream != NULL) {
        MsQuic->StreamShutdown(streamContext->quicStream, QUIC_STREAM_SHUTDOWN_FLAG_ABORT, 0);
        streamContext->quicStream = NULL;
    }

    // Release reference (this may destroy the context)
    StreamRelease(streamContext);

    return 0;
}

// Write data to a stream (internal implementation)
static int P2pStreamWriteInternal(P2P_STREAM stream, const char* buffer, int len) {
    P2P_STREAM_CONTEXT* streamContext = (P2P_STREAM_CONTEXT*)stream;
    if (streamContext == NULL || streamContext->quicStream == NULL ||
        streamContext->state != STREAM_STATE_OPEN || buffer == NULL || len <= 0) {
        return -1;
    }

    // For now, use simple direct send mode
    QUIC_BUFFER quicBuffer;
    quicBuffer.Buffer = (uint8_t*)buffer;
    quicBuffer.Length = len;

    QUIC_STATUS status = MsQuic->StreamSend(
        streamContext->quicStream,
        &quicBuffer,
        1,
        QUIC_SEND_FLAG_NONE,
        &quicBuffer
    );

    if (QUIC_FAILED(status)) {
        return -1;
    }

    return len;
}

// Write vectored data to a stream (internal implementation)
static int P2pStreamWritevInternal(P2P_STREAM stream, struct p2p_iovec* iov, int count) {
    P2P_STREAM_CONTEXT* streamContext = (P2P_STREAM_CONTEXT*)stream;
    if (streamContext == NULL || streamContext->quicStream == NULL ||
        streamContext->state != STREAM_STATE_OPEN || iov == NULL || count <= 0) {
        return -1;
    }

    // Convert p2p_iovec to QUIC_BUFFER
    QUIC_BUFFER* quicBuffers = (QUIC_BUFFER*)malloc(count * sizeof(QUIC_BUFFER));
    if (quicBuffers == NULL) {
        return -1;
    }

    int totalLen = 0;
    for (int i = 0; i < count; i++) {
        quicBuffers[i].Buffer = (uint8_t*)iov[i].iov_base;
        quicBuffers[i].Length = (uint32_t)iov[i].iov_len;
        totalLen += (int)iov[i].iov_len;
    }

    QUIC_STATUS status = MsQuic->StreamSend(
        streamContext->quicStream,
        quicBuffers,
        count,
        QUIC_SEND_FLAG_NONE,
        quicBuffers
    );

    free(quicBuffers);

    if (QUIC_FAILED(status)) {
        return -1;
    }

    return totalLen;
}

// Read data from a stream (internal implementation)
static int P2pStreamReadInternal(P2P_STREAM stream, char* buffer, int len) {
    P2P_STREAM_CONTEXT* streamContext = (P2P_STREAM_CONTEXT*)stream;
    if (streamContext == NULL || buffer == NULL || len <= 0) {
        return -1;
    }

    // For now, implement a simple read from the receive buffer
    // This is a simplified implementation - in a full implementation,
    // you would want to handle direct read mode and buffering properly

    CxPlatLockAcquire(&streamContext->bufferLock);

    int bytesRead = 0;
    if (streamContext->recvBuffer != NULL && streamContext->recvBuffer->used > 0) {
        bytesRead = RingBufferPeek(streamContext->recvBuffer, buffer, len);
        if (bytesRead > 0) {
            RingBufferConsume(streamContext->recvBuffer, bytesRead);
        }
    }

    CxPlatLockRelease(&streamContext->bufferLock);

    return bytesRead;
}

// Poll for stream events (internal implementation)
static int P2pStreamPollInternal(P2P_STREAM stream, struct StreamEvent* events, int max_events, int timeout) {
    P2P_STREAM_CONTEXT* streamContext = (P2P_STREAM_CONTEXT*)stream;
    if (streamContext == NULL || events == NULL || max_events <= 0) {
        return -1;
    }

    int eventCount = 0;

    // First, try to dequeue any existing events
    while (eventCount < max_events) {
        if (!DequeueStreamEvent(streamContext, &events[eventCount])) {
            break;
        }
        eventCount++;
    }

    // If we got events or timeout is 0, return immediately
    if (eventCount > 0 || timeout == 0) {
        return eventCount;
    }

    // Wait for events with timeout
    if (timeout > 0) {
        QUIC_STATUS status = CxPlatEventWaitWithTimeout(streamContext->eventAvailable, timeout);
        if (status == QUIC_STATUS_SUCCESS) {
            // Try to dequeue events again
            while (eventCount < max_events) {
                if (!DequeueStreamEvent(streamContext, &events[eventCount])) {
                    break;
                }
                eventCount++;
            }
        }
    }

    return eventCount;
}

// Get stream state (internal implementation)
static int P2pStreamGetStateInternal(P2P_STREAM stream) {
    P2P_STREAM_CONTEXT* streamContext = (P2P_STREAM_CONTEXT*)stream;
    if (streamContext == NULL) {
        return -1;
    }

    return streamContext->state;
}

// Set stream callback (internal implementation)
static int P2pStreamSetCallbackInternal(P2P_STREAM stream, StreamEventCallback callback, void* user_data) {
    P2P_STREAM_CONTEXT* streamContext = (P2P_STREAM_CONTEXT*)stream;
    if (streamContext == NULL) {
        return -1;
    }

    streamContext->callback = callback;
    streamContext->userData = user_data;

    return 0;
}

// Get stream ID (internal implementation)
static int P2pStreamGetIdInternal(P2P_STREAM stream) {
    P2P_STREAM_CONTEXT* streamContext = (P2P_STREAM_CONTEXT*)stream;
    if (streamContext == NULL) {
        return -1;
    }

    return (int)streamContext->streamId;
}

// Get buffered bytes count (internal implementation)
static int P2pStreamGetBufferedBytesInternal(P2P_STREAM stream) {
    P2P_STREAM_CONTEXT* streamContext = (P2P_STREAM_CONTEXT*)stream;
    if (streamContext == NULL) {
        return -1;
    }

    CxPlatLockAcquire(&streamContext->bufferLock);
    int bufferedBytes = 0;
    if (streamContext->recvBuffer != NULL) {
        bufferedBytes = streamContext->recvBuffer->used;
    }
    CxPlatLockRelease(&streamContext->bufferLock);

    return bufferedBytes;
}

// Get parent socket (internal implementation)
static P2P_SOCKET P2pStreamGetSocketInternal(P2P_STREAM stream) {
    P2P_STREAM_CONTEXT* streamContext = (P2P_STREAM_CONTEXT*)stream;
    if (streamContext == NULL) {
        return NULL;
    }

    return (P2P_SOCKET)streamContext->socket;
}

// Wrapper functions for QUIC Stream API (called from QuicSocket)
P2P_STREAM QuicStreamCreate(P2P_SOCKET soc, struct StreamOptions* options) {
    return P2pStreamCreateInternal(soc, options);
}

int QuicStreamClose(P2P_STREAM stream) {
    return P2pStreamCloseInternal(stream);
}

int QuicStreamWrite(P2P_STREAM stream, const char* buffer, int len) {
    return P2pStreamWriteInternal(stream, buffer, len);
}

int QuicStreamWritev(P2P_STREAM stream, struct p2p_iovec* iov, int count) {
    return P2pStreamWritevInternal(stream, iov, count);
}

int QuicStreamRead(P2P_STREAM stream, char* buffer, int len) {
    return P2pStreamReadInternal(stream, buffer, len);
}

int QuicStreamPoll(P2P_STREAM stream, struct StreamEvent* events, int max_events, int timeout) {
    return P2pStreamPollInternal(stream, events, max_events, timeout);
}

int QuicStreamGetState(P2P_STREAM stream) {
    return P2pStreamGetStateInternal(stream);
}

int QuicStreamSetCallback(P2P_STREAM stream, StreamEventCallback callback, void* user_data) {
    return P2pStreamSetCallbackInternal(stream, callback, user_data);
}

int QuicStreamGetId(P2P_STREAM stream) {
    return P2pStreamGetIdInternal(stream);
}

int QuicStreamGetBufferedBytes(P2P_STREAM stream) {
    return P2pStreamGetBufferedBytesInternal(stream);
}

P2P_SOCKET QuicStreamGetSocket(P2P_STREAM stream) {
    return P2pStreamGetSocketInternal(stream);
}

// Public Stream API functions (called from p2psocket.cpp)
P2P_STREAM P2pStreamCreate(P2P_SOCKET soc, struct StreamOptions* options) {
    return P2pStreamCreateInternal(soc, options);
}

int P2pStreamClose(P2P_STREAM stream) {
    return P2pStreamCloseInternal(stream);
}

int P2pStreamWrite(P2P_STREAM stream, const char* buffer, int len) {
    return P2pStreamWriteInternal(stream, buffer, len);
}

int P2pStreamWritev(P2P_STREAM stream, struct p2p_iovec* iov, int count) {
    return P2pStreamWritevInternal(stream, iov, count);
}

int P2pStreamRead(P2P_STREAM stream, char* buffer, int len) {
    return P2pStreamReadInternal(stream, buffer, len);
}

int P2pStreamPoll(P2P_STREAM stream, struct StreamEvent* events, int max_events, int timeout) {
    return P2pStreamPollInternal(stream, events, max_events, timeout);
}

int P2pStreamGetState(P2P_STREAM stream) {
    return P2pStreamGetStateInternal(stream);
}

int P2pStreamSetCallback(P2P_STREAM stream, StreamEventCallback callback, void* user_data) {
    return P2pStreamSetCallbackInternal(stream, callback, user_data);
}

int P2pStreamGetId(P2P_STREAM stream) {
    return P2pStreamGetIdInternal(stream);
}

int P2pStreamGetBufferedBytes(P2P_STREAM stream) {
    return P2pStreamGetBufferedBytesInternal(stream);
}

P2P_SOCKET P2pStreamGetSocket(P2P_STREAM stream) {
    return P2pStreamGetSocketInternal(stream);
}

// New stream callback for Stream API
QUIC_STATUS
NewStreamCallback(HQUIC Stream, void* Context, QUIC_STREAM_EVENT* Event) {
    P2P_STREAM_CONTEXT* streamContext = (P2P_STREAM_CONTEXT*)Context;

    if (streamContext == NULL) {
        return QUIC_STATUS_INVALID_PARAMETER;
    }

    switch (Event->Type) {
    case QUIC_STREAM_EVENT_RECEIVE:
        // Process received data
        if (Event->RECEIVE.BufferCount > 0 && Event->RECEIVE.Buffers != NULL) {
            // Calculate total data length
            uint64_t totalLength = 0;
            for (uint32_t i = 0; i < Event->RECEIVE.BufferCount; i++) {
                totalLength += Event->RECEIVE.Buffers[i].Length;
            }

            LLog::Log(LOG_INFO, "stream:%p, receiving %d bytes", streamContext, totalLength);

            // For now, use copy mode - copy data to stream's receive buffer
            CxPlatLockAcquire(&streamContext->bufferLock);

            // Ensure we have a receive buffer
            if (streamContext->recvBuffer == NULL) {
                streamContext->recvBuffer = (BUFFER_LIST*)P2pMalloc(sizeof(BUFFER_LIST));
                if (streamContext->recvBuffer != NULL) {
                    if (RingBufferInit(streamContext->recvBuffer, streamContext->options.buffer_size) != 0) {
                        free(streamContext->recvBuffer);
                        streamContext->recvBuffer = NULL;
                        CxPlatLockRelease(&streamContext->bufferLock);
                        return QUIC_STATUS_OUT_OF_MEMORY;
                    }
                    streamContext->recvBufferEnd = streamContext->recvBuffer;
                }
            }

            // Copy data to receive buffer
            if (streamContext->recvBuffer != NULL) {
                for (uint32_t i = 0; i < Event->RECEIVE.BufferCount; i++) {
                    RingBufferWrite(streamContext->recvBuffer,
                                  (const char*)Event->RECEIVE.Buffers[i].Buffer,
                                  Event->RECEIVE.Buffers[i].Length);
                }
            }

            CxPlatLockRelease(&streamContext->bufferLock);

            // Update state and signal data availability
            streamContext->dataAvailable = 1;
            CxPlatEventSet(streamContext->recvEvent);

            // Queue stream event for poll mode
            struct StreamEvent streamEvent;
            streamEvent.type = STREAM_EVENT_DATA_RECEIVED;
            streamEvent.stream = (P2P_STREAM)streamContext;
            streamEvent.data_received.data = NULL; // Data is in buffer
            streamEvent.data_received.length = (int)totalLength;
            QueueStreamEvent(streamContext, &streamEvent);
        }
        break;

    case QUIC_STREAM_EVENT_SEND_COMPLETE: {
        // Mark send as complete
        streamContext->sendComplete = 1;
        CxPlatEventSet(streamContext->sendEvent);

        // Queue stream event for poll mode
        struct StreamEvent streamEvent;
        streamEvent.type = STREAM_EVENT_SEND_COMPLETE;
        streamEvent.stream = (P2P_STREAM)streamContext;
        streamEvent.send_complete.bytes_sent = 0; // Could get from Event if needed
        streamEvent.send_complete.error_code = 0;
        QueueStreamEvent(streamContext, &streamEvent);

        LLog::Log(LOG_INFO, "stream:%p, send complete", streamContext);
    } break;

    case QUIC_STREAM_EVENT_PEER_SEND_ABORTED:
    case QUIC_STREAM_EVENT_PEER_RECEIVE_ABORTED:
        // Peer closed the stream
        streamContext->state = STREAM_STATE_CLOSED;

        // Queue stream event for poll mode
        struct StreamEvent streamEvent;
        streamEvent.type = STREAM_EVENT_PEER_CLOSED;
        streamEvent.stream = (P2P_STREAM)streamContext;
        streamEvent.error.error_code = 0;
        QueueStreamEvent(streamContext, &streamEvent);

        LLog::Log(LOG_INFO, "stream:%p, peer closed", streamContext);
        break;

    case QUIC_STREAM_EVENT_SHUTDOWN_COMPLETE:
        LLog::Log(LOG_INFO, "stream:%p, shutdown complete", streamContext);
        streamContext->state = STREAM_STATE_CLOSED;
        break;

    default:
        break;
    }

    return QUIC_STATUS_SUCCESS;
}
