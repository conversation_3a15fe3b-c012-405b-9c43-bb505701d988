// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#pragma once
#ifdef FS_STATIC_LIB
#define PLUGIN_API
#else
#if defined(WIN32) || defined(_WIN32)
#ifdef DLL_EXPORT
#define PLUGIN_API __declspec(dllexport)
#else
#define PLUGIN_API __declspec(dllimport)
#endif
#endif

#if defined(ANDROID) || defined(__linux__)
#define PLUGIN_API __attribute__((visibility("default")))
#endif
#endif

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#ifndef _WIN32
typedef struct Cert_t* CertHandle;
PLUGIN_API CertHandle Cert_Create();
PLUGIN_API void Cert_Destroy(CertHandle handle);

PLUGIN_API const char* Cert_GetLocalX509CertFingerprint(CertHandle handle);
PLUGIN_API const char* Cert_GetPkeyStr(CertHandle handle);
PLUGIN_API const char* Cert_GetX509Str(CertHandle handle);
#endif
#ifdef __cplusplus
}
#endif