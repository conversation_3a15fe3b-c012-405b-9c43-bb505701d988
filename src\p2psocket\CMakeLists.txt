# P2PSocket Library CMake Configuration
cmake_minimum_required(VERSION 3.16)


set(P2PSOCKET_LIB_NAME p2psocket)


set(P2PSOCKET_SOURCES
    p2psocket.cpp
    lenlog.h
    lenlog.cpp
    p2pquic.h
    p2pquic.cpp
    socket.h
    socket.cpp
    p2psocketfactory.h
    p2psocketfactory.cpp
    quicsocket.h
    quicsocket.cpp
    cert_wrapper.h
    cert_wrapper.cpp
    cert.h
    cert.cpp
)

set(P2PSOCKET_HEADERS
    p2psocket.h
    common_defines.h
)


add_library(${P2PSOCKET_LIB_NAME} SHARED ${P2PSOCKET_SOURCES} ${P2PSOCKET_HEADERS})


target_include_directories(${P2PSOCKET_LIB_NAME} PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/inc
)


target_link_libraries(${P2PSOCKET_LIB_NAME} PRIVATE
    msquic_static
    core
    msquic_platform
)


target_compile_definitions(${P2PSOCKET_LIB_NAME} PRIVATE
    DLL_EXPORT
    QUIC_BUILD_STATIC
)

if(WIN32)
    target_compile_definitions(${P2PSOCKET_LIB_NAME} PRIVATE
        _WINDOWS
        _USRDLL
        _WINDLL
        WIN32_LEAN_AND_MEAN
    )

    target_link_libraries(${P2PSOCKET_LIB_NAME} PRIVATE
        ws2_32
        advapi32
        crypt32
        iphlpapi
        ntdll
        secur32
        bcrypt
        ncrypt
        msvcrt
        kernel32
        user32
    )
else()
    target_compile_options(${P2PSOCKET_LIB_NAME} PRIVATE -fPIC)

    if(ANDROID)
        # Android doesn't have a separate pthread library
        target_link_libraries(${P2PSOCKET_LIB_NAME} PRIVATE
            dl
            log
        )
    else()
        target_link_libraries(${P2PSOCKET_LIB_NAME} PRIVATE
            pthread
            dl
        )
    endif()
endif()

set_target_properties(${P2PSOCKET_LIB_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

install(TARGETS ${P2PSOCKET_LIB_NAME}
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(FILES ${P2PSOCKET_HEADERS} DESTINATION include)

add_executable(test_p2psocket test_p2psocket.c)
target_link_libraries(test_p2psocket PRIVATE ${P2PSOCKET_LIB_NAME})
target_include_directories(test_p2psocket PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

set_target_properties(test_p2psocket PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

install(TARGETS test_p2psocket
    RUNTIME DESTINATION bin
)

# Add testcapability executable
add_executable(testcapability testcapability.cpp)
target_link_libraries(testcapability PRIVATE ${P2PSOCKET_LIB_NAME})
target_include_directories(testcapability PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

if(WIN32)
    target_link_libraries(testcapability PRIVATE
        ws2_32
        advapi32
        crypt32
    )
    target_compile_options(${P2PSOCKET_LIB_NAME} PRIVATE
    $<$<CONFIG:Debug>:/MD>
    $<$<CONFIG:Release>:/MD>
)
endif()

set_target_properties(testcapability PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

install(TARGETS testcapability
    RUNTIME DESTINATION bin
)

# Add test_multistream executable
add_executable(test_multistream test_multistream.cpp)
target_link_libraries(test_multistream PRIVATE ${P2PSOCKET_LIB_NAME})
target_include_directories(test_multistream PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

# Set C++17 standard for test_multistream (required for std::thread and other features)
set_property(TARGET test_multistream PROPERTY CXX_STANDARD 17)
set_property(TARGET test_multistream PROPERTY CXX_STANDARD_REQUIRED ON)

if(WIN32)
    target_link_libraries(test_multistream PRIVATE
        ws2_32
        advapi32
        crypt32
    )
    target_compile_options(test_multistream PRIVATE
        $<$<CONFIG:Debug>:/MD>
        $<$<CONFIG:Release>:/MD>
    )
else()
    # For Linux/Unix, link pthread for std::thread support
    if(NOT ANDROID)
        target_link_libraries(test_multistream PRIVATE
            pthread
        )
    endif()
endif()

set_target_properties(test_multistream PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

install(TARGETS test_multistream
    RUNTIME DESTINATION bin
)

# Add test_stream_api executable
add_executable(test_stream_api test_stream_api.c)
target_link_libraries(test_stream_api PRIVATE ${P2PSOCKET_LIB_NAME})
target_include_directories(test_stream_api PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

if(WIN32)
    target_link_libraries(test_stream_api PRIVATE
        ws2_32
        advapi32
        crypt32
    )
    target_compile_options(test_stream_api PRIVATE
        $<$<CONFIG:Debug>:/MD>
        $<$<CONFIG:Release>:/MD>
    )
endif()

set_target_properties(test_stream_api PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

install(TARGETS test_stream_api
    RUNTIME DESTINATION bin
)

# Add stream_api_example executable
add_executable(stream_api_example stream_api_example.c)
target_link_libraries(stream_api_example PRIVATE ${P2PSOCKET_LIB_NAME})
target_include_directories(stream_api_example PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})

if(WIN32)
    target_link_libraries(stream_api_example PRIVATE
        ws2_32
        advapi32
        crypt32
    )
    target_compile_options(stream_api_example PRIVATE
        $<$<CONFIG:Debug>:/MD>
        $<$<CONFIG:Release>:/MD>
    )
endif()

set_target_properties(stream_api_example PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

install(TARGETS stream_api_example
    RUNTIME DESTINATION bin
)
